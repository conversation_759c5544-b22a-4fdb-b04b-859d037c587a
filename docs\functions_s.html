<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Class Members</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('functions_s.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all class members with links to the classes they belong to:</div>

<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>sandboxDisabled()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a8abeb109ccc2c7971afb98efee06735e">QCefConfig</a></li>
<li>sansSerifFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a861fccc511473f01f74a5199d2660126">QCefSetting</a></li>
<li>serifFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a43ab6247cbbaa59652846eb84760c1fb">QCefSetting</a></li>
<li>setAcceptLanguageList()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a360c26dd512b9a4a3d6596c0590c370b">QCefConfig</a></li>
<li>setArguments()&#160;:&#160;<a class="el" href="class_q_cef_event.html#ac84ba1292bcf56abdc5c6c4245aa6c04">QCefEvent</a></li>
<li>setBackgroundColor()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a2ef252883876dd17193212c52bd02fc0">QCefConfig</a>, <a class="el" href="class_q_cef_setting.html#a6dbd7b1da3a151294e8bf020a16687be">QCefSetting</a></li>
<li>setBridgeObjectName()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a03687393e227bc8747bdc9ffa7400d60">QCefConfig</a></li>
<li>setBrowserSubProcessPath()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a3fca1b7b72f37f800278c743b74f1b82">QCefConfig</a></li>
<li>setBuiltinSchemeName()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a7e84d09e2bcacfc5fdfb8eeca49aca98">QCefConfig</a></li>
<li>setCachePath()&#160;:&#160;<a class="el" href="class_q_cef_config.html#aa8f73284ec9ed73dc2028b8c89e8e3c8">QCefConfig</a></li>
<li>setCommandLinePassthroughDisabled()&#160;:&#160;<a class="el" href="class_q_cef_config.html#aefbb555266937e769ed2102df41b0599">QCefConfig</a></li>
<li>setCursiveFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#aef4eb96f03003eb774924fe418a7edf1">QCefSetting</a></li>
<li>setDatabases()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#ad0680b646641dd568b7de8ae072670db">QCefSetting</a></li>
<li>setDefaultEncoding()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a599a011dff8d11c8201036238016a77f">QCefSetting</a></li>
<li>setDefaultFixedFontSize()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a4dddf809104f676469fc03b266d7a2ff">QCefSetting</a></li>
<li>setDefaultFontSize()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a85b9cadc6df83a3addbaf567df791339">QCefSetting</a></li>
<li>setDisablePopupContextMenu()&#160;:&#160;<a class="el" href="class_q_cef_view.html#acca71443b26dce09e81e3f937cedaa6b">QCefView</a></li>
<li>setEnableDragAndDrop()&#160;:&#160;<a class="el" href="class_q_cef_view.html#af73ef1d6f77a31b528c729cf7379abfb">QCefView</a></li>
<li>setEventName()&#160;:&#160;<a class="el" href="class_q_cef_event.html#a5db1273fdda416900f5b7b26a119c85a">QCefEvent</a></li>
<li>setFantasyFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a831bed0440aed06894a85ee8dde74a05">QCefSetting</a></li>
<li>setFixedFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#af11ccd0529a79efee12a3e728d24e641">QCefSetting</a></li>
<li>setFocus()&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9b1b42857e38a9f5c6c810fd51593788">QCefView</a></li>
<li>setHardwareAcceleration()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#afdb320899b859e7781458a281a9dafbe">QCefSetting</a></li>
<li>setImageLoading()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a15457b991b298a722cbc9f9507d109fb">QCefSetting</a></li>
<li>setImageShrinkStandaloneToFit()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a562e4477613234a906b2d167473b0627">QCefSetting</a></li>
<li>setJavascript()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a884abf03a17dc3fa4343b578445219c0">QCefSetting</a></li>
<li>setJavascriptAccessClipboard()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#aff0a7e72f55a002f5731f4e202e45d63">QCefSetting</a></li>
<li>setJavascriptCloseWindows()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a7c3755e100310ab63a98cbd6b7c89a6b">QCefSetting</a></li>
<li>setJavascriptDomPaste()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a61c6207fc6fb05b71248fee8766d21a2">QCefSetting</a></li>
<li>setLocale()&#160;:&#160;<a class="el" href="class_q_cef_config.html#af67e837996a1dd84af0866f76588ba4e">QCefConfig</a></li>
<li>setLocalesDirectoryPath()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a4af04a575ecd6b632a794c42144d03d8">QCefConfig</a></li>
<li>setLocalStorage()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#afca6695cdffbb1734588c33ffff3aa3c">QCefSetting</a></li>
<li>setLogLevel()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a230ee52b4d64e0ea6f7ba5a4e9ac5f5e">QCefConfig</a></li>
<li>setMinimumFontSize()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a0c1733e2e173cb462f0ec21a613b628e">QCefSetting</a></li>
<li>setMinimumLogicalFontSize()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#aa6e22cc3cfa68ad13809b6766e9cafab">QCefSetting</a></li>
<li>setPersistSessionCookies()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a04c4f9aa52131df29c4eb6abd48cc2f0">QCefConfig</a></li>
<li>setPersistUserPreferences()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a6c5c7d498a6c003166071ac6e4e7e359">QCefConfig</a></li>
<li>setPreference()&#160;:&#160;<a class="el" href="class_q_cef_view.html#a2b4b3da8874855bbe2d558081233d948">QCefView</a></li>
<li>setRemoteDebuggingPort()&#160;:&#160;<a class="el" href="class_q_cef_config.html#ac502d5e4b911c4e57d6fe4167be6d801">QCefConfig</a></li>
<li>setRemoteFonts()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#aba88a474960049cda01c7295e514eb8a">QCefSetting</a></li>
<li>setResourceDirectoryPath()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a0690fb1cb1a3cd87c44be340b6308f42">QCefConfig</a></li>
<li>setResponseResult()&#160;:&#160;<a class="el" href="class_q_cef_query.html#aa86db4e257e3dc4e29c7906d80e06f28">QCefQuery</a></li>
<li>setRootCachePath()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a768b9bc0368ac7a82f6e74aec536aa8f">QCefConfig</a></li>
<li>setSandboxDisabled()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a09f3800b8911bad084b9e4673f1839b0">QCefConfig</a></li>
<li>setSansSerifFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#aa07d27c4a22dc2ec0d041c9deda1d71b">QCefSetting</a></li>
<li>setSerifFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a9eadb4d8d6567c78d80f09e1ace1dd30">QCefSetting</a></li>
<li>setStandardFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#ad021537af966fb8f17d8a07066a5408e">QCefSetting</a></li>
<li>setTabToLinks()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a8ccb815304eeadba9d679186472d4e40">QCefSetting</a></li>
<li>setTextAreaResize()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a159239d8d7d5b4f944db0c9f37b10509">QCefSetting</a></li>
<li>setUserAgent()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a60009aad390599eb5857182a32de7f23">QCefConfig</a></li>
<li>setWebGL()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a984a074332bd07b78da2079d1d333209">QCefSetting</a></li>
<li>setWindowInitialSize()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a629a1139dff88c3bb85a4c9a620d0682">QCefSetting</a></li>
<li>setWindowlessFrameRate()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a4a5810da8e070288ff80c069f5b52f23">QCefSetting</a></li>
<li>setWindowlessRenderingEnabled()&#160;:&#160;<a class="el" href="class_q_cef_config.html#af6041bcae9fcf72ea47ffc47d62e5a96">QCefConfig</a></li>
<li>showDevTools()&#160;:&#160;<a class="el" href="class_q_cef_view.html#a61845e6e370a57be5f3662ba37cd7b29">QCefView</a></li>
<li>showEvent()&#160;:&#160;<a class="el" href="class_q_cef_view.html#a08dcba31e0d2860270ab3cd8055a5c4e">QCefView</a></li>
<li>standardFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#aa7a0cfa4086251bdfc95c4ae72e52896">QCefSetting</a></li>
<li>start()&#160;:&#160;<a class="el" href="class_q_cef_download_item.html#a315592aa53a2bf7bc8aea717195f5b43">QCefDownloadItem</a></li>
<li>startTime()&#160;:&#160;<a class="el" href="class_q_cef_download_item.html#afe0d048a282cb605da910de1c5d82242">QCefDownloadItem</a></li>
<li>statusMessage()&#160;:&#160;<a class="el" href="class_q_cef_view.html#a86d10c28b8821a36723e3504fa0cc7e7">QCefView</a></li>
<li>suggestedFileName()&#160;:&#160;<a class="el" href="class_q_cef_download_item.html#ac0dd9f8bea7ca594f04935d81cfb72a4">QCefDownloadItem</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
