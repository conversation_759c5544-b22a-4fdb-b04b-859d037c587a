<html>

<head>
    <script>
        console.log("page script starts.....");

        window.onwheel = e => console.log(`wheel event: altKey=${e.altKey} ctrlKey=${e.ctrlKey}`);

        function onLoad() {
            if (typeof CallBridge == "undefined") {
                alert("Not in CefView context");
                return;
            }
        }

        function onChangeColorEvent(color) {
            console.log("request change background color to: ", color);
            // change the background color
            document.getElementById("main").style.backgroundColor = color;
        }

        function onBuiltInSchemaHandler() {
            fetch("CefView://custom.scheme.handler/api/example?arg1=1&args=hello")
                .then((response) => {
                    alert(`Native Response:${response}`)
                });;
        }

        function onAddEventListener() {
            // Add a event listener to handle the event named 'colorChange'
            CallBridge.addEventListener(
                // event name
                "colorChange",
                // event handler
                onChangeColorEvent
            );
        }

        function onRemoveEventListener() {
            // Add a event listener to handle the event named 'colorChange'
            CallBridge.removeEventListener(
                // event name
                "colorChange",
                // event handler
                onChangeColorEvent
            );
        }

        function onAlert() {
            alert('JSDialog: alert');
        }

        function onConfirm() {
            if (confirm('JSDialog: confirm')) {
                alert("JSDialog: confirm was accepted by clicking ok");
            } else {
                alert("JSDialog: confirm was canceled");
            }
        }

        function onPrompt() {
            let name = prompt("JSDialog: prompt: Please input your name", "Your Name Here");
            if (name === null) {
                alert("JSDialog: prompt was canceled");
            } else {
                alert(`JSDialog: prompt was accepted and hello ${name}`);
            }
        }

        function onInvokeMethodClicked(name, ...arg) {
            CallBridge.invoke(name, ...arg);
        }

        function onCallBridgeQueryClicked() {
            var query = {
                request: document.getElementById("message").value,
                onSuccess: function (response) {
                    alert(response);
                },
                onFailure: function (error_code, error_message) {
                    alert(error_message);
                },
            };
            window.cefViewQuery(query);
        }

        function testInvokeMethod() {
            let d = {
                d1: true,
                d2: 5678,
                d3: "test object",
                d4: [1, "2", false],
                d5: {
                    d1: true,
                    d2: 5678,
                    d3: "nested object",
                    d4: [1, "2", true],
                },
            };
            onInvokeMethodClicked("TestMethod", 1, false, "arg3", d);
        }
    </script>
    <style>
        .noselect {
            user-select: none;
            /* Non-prefixed version, currently supported by Chrome, Edge, Opera and Firefox */
        }
    </style>
</head>

<body onload="onLoad()" id="main" class="noselect">
    <div align="left">
        <label> Test Case for Built-in Schema Handler (default cefview://) </label>
        <br />
        <input type="button" value="Request Built-in Scheme Handler (CefView://)" onclick="onBuiltInSchemaHandler()" />
        <br />
        <a href="cefview://custom.scheme.handler/api/example?arg1=1&args=hello">Built-in Scheme Handler (CefView://)</a>
        <br />
        <br />

        <label> Test Case for EventListener (changeColor)</label>
        <br />
        <input type="button" value="AddEventListener" onclick="onAddEventListener()" />
        <input type="button" value="RemoveEventListener" onclick="onRemoveEventListener()" />
        <br />
        <br />

        <label> Test Case for JSDialog</label>
        <br />
        <input type="button" value="Alert" onclick="onAlert()" />
        <input type="button" value="Confirm" onclick="onConfirm()" />
        <input type="button" value="Promt" onclick="onPrompt()" />
        <br />
        <br />

        <label> Test Case for invoking native method </label>
        <br />
        <input type="button" value="invoke()" onclick="testInvokeMethod()" />
        <br />
        <br />

        <label> Test Case for QCefQuery </label>
        <br />
        <textarea id="message"
                  style="width: 320px; height: 120px">this message will be processed by native code.</textarea>
        <br />
        <input type="button" value="Query" onclick="onCallBridgeQueryClicked()" />
        <br />
        <br />

        <label> Test Case for Popup Browser </label>
        <br />
        <a href="#" target="_blank">Popup Browser By HTML</a>
        <br />
        <a href="#" onClick="window.open('#','QCefView Popup','width=800, height=600'); return false;">
            Popup Browser By Script
        </a>

        <br />
        <a href="#" onClick="window.close();">Close Current Window</a>
    </div>
</body>

</html>