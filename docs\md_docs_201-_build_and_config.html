<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Build and Config</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('md_docs_201-_build_and_config.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Build and Config</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul>
  <li class="level1">
    <a href="#autotoc_md8">Build</a>
    <ul>
      <li class="level2">
        <a href="#autotoc_md9">Prerequisites</a>
        <ul>
          <li class="level3">
            <a href="#autotoc_md10">1. CMake</a>
          </li>
          <li class="level3">
            <a href="#autotoc_md11">2. Qt Framework</a>
          </li>
        </ul>
      </li>
      <li class="level2">
        <a href="#autotoc_md12">Build QCefView</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md13">Additional Notes</a>
      </li>
    </ul>
  </li>
  <li class="level1">
    <a href="#autotoc_md14">Config</a>
    <ul>
      <li class="level2">
        <a href="#autotoc_md15">QT_SDK_DIR</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md16">CEF_SDK_VERSION</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md17">CUSTOM_CEF_SDK_DIR</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md18">BUILD_DEMO</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md19">BUILD_STATIC</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md20">STATIC_CRT</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md21">USE_SANDBOX</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md22">USE_WIN_DCOMPOSITION</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md23">USE_QT_EVENT_LOOP</a>
      </li>
    </ul>
  </li>
</ul>
</div>
<div class="textblock"><p><a class="anchor" id="autotoc_md7"></a></p>
<h1><a class="anchor" id="autotoc_md8"></a>
Build</h1>
<p>This guide provides detailed instructions on how to build the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> project from source.</p>
<h2><a class="anchor" id="autotoc_md9"></a>
Prerequisites</h2>
<p>Before you begin, ensure you have the following software installed and configured:</p>
<h3><a class="anchor" id="autotoc_md10"></a>
1. CMake</h3>
<p><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> uses <a href="https://cmake.org/">CMake</a> to manage the build process. CMake is a cross-platform, open-source build system generator.</p>
<ul>
<li><b>Installation:</b> Download and install CMake from the <a href="https://cmake.org/download/">official CMake website</a>. The minimum supported version is 3.19.1, but we recommend using the latest version for optimal compatibility and features.</li>
</ul>
<h3><a class="anchor" id="autotoc_md11"></a>
2. Qt Framework</h3>
<p><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> is built upon the Qt framework, offering support for both Qt 5.x and 6.x.</p>
<ul>
<li><b>Installation:</b> Download and install the Qt framework from the <a href="https://www.qt.io/download">Qt website</a>.</li>
<li><p class="startli"><b>Environment Variable:</b> After installation, set the <code>QTDIR</code> environment variable to point to your Qt installation directory. This allows CMake to locate the necessary Qt files.</p>
<p class="startli"><b>Examples:</b></p><ul>
<li><p class="startli"><b>Windows:</b></p>
<p class="startli"><code>bat set QTDIR=C:\Qt\6.2.2\msvc2019_64 </code></p>
</li>
<li><p class="startli"><b>macOS:</b></p>
<p class="startli"><code>bash export QTDIR=/usr/local/Cellar/qt5/5.4.1/clang_64 </code></p>
</li>
<li><p class="startli"><b>Linux:</b></p>
<p class="startli"><code>bash export QTDIR=/opt/Qt/6.2.2/gcc_64 # Example path, adjust accordingly </code></p>
</li>
</ul>
</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>Adjust the paths in the examples above to match your actual Qt installation location.</dd></dl>
<h2><a class="anchor" id="autotoc_md12"></a>
Build QCefView</h2>
<ol type="1">
<li><p class="startli"><b>Clone the Repository:</b></p>
<p class="startli">Clone the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> repository from GitHub, including its submodules:</p>
<p class="startli"><code>bash git clone --recursive <a href="https://github.com/CefView/QCefView.git">https://github.com/CefView/QCefView.git</a> cd <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> </code></p>
<p class="startli">The <code>--recursive</code> flag ensures that the necessary <code>CefViewCore</code> submodule is also initialized and cloned. If you forgot to use <code>--recursive</code>, you can initialize the submodule manually:</p>
<p class="startli"><code>bash git submodule init git submodule update </code></p>
</li>
<li><p class="startli"><b>Generate Build Files:</b></p>
<p class="startli">Use the appropriate script for your operating system to generate the build files.</p><ul>
<li><p class="startli"><b>Windows (Visual Studio):</b></p>
<p class="startli"><code>bash generate-win-x86_64.bat </code></p>
</li>
<li><p class="startli"><b>macOS (Xcode):</b></p>
<p class="startli"><code>bash ./generate-mac-x86_64.sh </code></p>
</li>
<li><p class="startli"><b>Linux (Unix Makefiles):</b></p>
<p class="startli"><code>bash ./generate-linux-x86_64.sh </code></p>
</li>
</ul>
</li>
<li><p class="startli"><b>Build the Project:</b></p>
<p class="startli">Use CMake to build the project.</p><ul>
<li><p class="startli"><b>Windows:</b></p>
<p class="startli"><code>bash cmake --build .build/windows.x86_64 --config Release # Or Debug </code></p>
</li>
<li><p class="startli"><b>macOS:</b></p>
<p class="startli"><code>bash cmake --build .build/macos.x86_64 --config Release # Or Debug </code></p>
</li>
<li><p class="startli"><b>Linux:</b></p>
<p class="startli"><code>bash cmake --build .build/linux.x86_64 </code></p>
</li>
</ul>
</li>
<li><p class="startli"><b>Locate the Project File:</b></p>
<p class="startli">The generated project files will be located in the <code>.build</code> directory, within a subdirectory specific to your operating system and architecture (e.g., <code>.build/windows.x86_64</code> or <code>.build/macos.x86_64</code>).</p><ul>
<li><b>Windows:</b> You can open the generated Visual Studio solution file (<code>.sln</code>) and build the project from within Visual Studio.</li>
<li><b>macOS:</b> You can open the generated Xcode project file (<code>.xcodeproj</code>) and build the project from within Xcode.</li>
<li><b>Linux:</b> Qt Creator is the recommended IDE for Linux development. You can import the CMake project into Qt Creator.</li>
</ul>
</li>
</ol>
<h2><a class="anchor" id="autotoc_md13"></a>
Additional Notes</h2>
<ul>
<li><b>Build Configurations:</b> The CMake build command includes a <code>--config</code> option. You can specify <code>Release</code> for an optimized build or <code>Debug</code> for a build with debugging symbols.</li>
<li><b>Qt Creator (Linux):</b> When using Qt Creator on Linux, select "Import Existing Project" and choose the top-level <code>CMakeLists.txt</code> file.</li>
</ul>
<p>By following these instructions, you should be able to successfully build the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> project on your platform. If you encounter any issues, please consult the project's documentation or community forums for assistance.</p>
<h1><a class="anchor" id="autotoc_md14"></a>
Config</h1>
<p><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> offers several CMake options to customize your build. These options allow you to tailor the build process to your specific needs and environment.</p>
<p>You can set these CMake options when you generate the build files. The method depends on your build environment:</p>
<ul>
<li><b>CMake GUI:</b> Use the CMake GUI to configure the options before generating the project files.</li>
<li><p class="startli"><b>Command Line:</b> Use the <code>-D</code> flag when running CMake from the command line. For example:</p>
<p class="startli"><code>bash cmake -DCEF_SDK_VERSION="106.0.5249.61" -DBUILD_DEMO=OFF .. </code></p>
</li>
<li><b>CMakePresets.json:</b> If you are using CMakePresets.json, you can set the options in the <code>cacheVariables</code> section.</li>
</ul>
<p>By understanding and utilizing these configuration options, you can optimize the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> build process for your specific application and platform.</p>
<p>Here's a detailed overview of the available CMake configuration options:</p>
<h2><a class="anchor" id="autotoc_md15"></a>
QT_SDK_DIR</h2>
<ul>
<li><b>Description:</b> Specifies the directory containing the Qt SDK.</li>
<li><b>Values:</b> The absolute path to your Qt installation directory (e.g., <code>C:/Qt/6.2.4/msvc2019_64</code> on Windows, <code>/opt/Qt/6.2.4/gcc_64</code> on Linux).</li>
<li><b>Default:</b> CMake attempts to automatically detect the Qt SDK.</li>
<li><b>Usage:</b> Set this option if CMake fails to locate your Qt installation, or if you want to use a specific Qt version.</li>
</ul>
<h2><a class="anchor" id="autotoc_md16"></a>
CEF_SDK_VERSION</h2>
<ul>
<li><b>Description:</b> Specifies the version of the Chromium Embedded Framework (CEF) SDK to use.</li>
<li><b>Values:</b> A string representing the CEF SDK version (e.g., <code>"112.3.0+gb09c4ca+chromium-112.0.5615.165"</code>).</li>
<li><b>Default:</b> The version defined in the root <code>CMakeLists.txt</code> file.</li>
<li><b>Usage:</b> Set this option if you need to use a specific CEF SDK version that differs from the default.</li>
</ul>
<dl class="section remark"><dt>Remarks</dt><dd>Find the pre-compiled CEF SDK from <a href="https://cef-builds.spotifycdn.com/index.html">https://cef-builds.spotifycdn.com/index.html</a> (Powered by Spotify). They serve the pre-compiled CEF binaries with official code. These binaries are compiled with some features disabled (like video decoders). If you need these features, you need to compile CEF binaries.</dd></dl>
<h2><a class="anchor" id="autotoc_md17"></a>
CUSTOM_CEF_SDK_DIR</h2>
<ul>
<li><b>Description:</b> Specifies the directory containing a custom-built CEF SDK.</li>
<li><b>Values:</b> The absolute path to your custom CEF SDK directory.</li>
<li><b>Default:</b> Empty. If not set, the build process will attempt to download a pre-built CEF SDK based on <code>CEF_SDK_VERSION</code>.</li>
<li><b>Usage:</b> Set this option if you have built your own CEF SDK and want to use it instead of the pre-built version. This is useful for advanced users who need to customize CEF.</li>
</ul>
<h2><a class="anchor" id="autotoc_md18"></a>
BUILD_DEMO</h2>
<ul>
<li><b>Description:</b> Enables or disables the building of the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> demo application.</li>
<li><b>Values:</b> <code>ON</code> (enable) or <code>OFF</code> (disable).</li>
<li><b>Default:</b> <code>OFF</code></li>
<li><b>Usage:</b> Disable this option if you only need the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> library and not the demo application.</li>
</ul>
<h2><a class="anchor" id="autotoc_md19"></a>
BUILD_STATIC</h2>
<ul>
<li><b>Description:</b> Configures <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> to be built as a static library.</li>
<li><b>Values:</b> <code>ON</code> (enable) or <code>OFF</code> (disable).</li>
<li><b>Default:</b> <code>OFF</code></li>
<li><b>Usage:</b> Enable this option if you want to link <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> statically into your application. Note that this might require additional configuration depending on your project setup.</li>
</ul>
<h2><a class="anchor" id="autotoc_md20"></a>
STATIC_CRT</h2>
<ul>
<li><b>Description:</b> Links the C runtime library statically.</li>
<li><b>Values:</b> <code>ON</code> (enable) or <code>OFF</code> (disable).</li>
<li><b>Default:</b> <code>OFF</code></li>
<li><b>Usage:</b> Enable this option to avoid dependencies on the Visual C++ runtime DLLs. This is primarily relevant on Windows. Using this option can increase the size of your executable.</li>
</ul>
<h2><a class="anchor" id="autotoc_md21"></a>
USE_SANDBOX</h2>
<ul>
<li><b>Description:</b> Enables or disables the CEF sandbox.</li>
<li><b>Values:</b> <code>ON</code> (enable) or <code>OFF</code> (disable).</li>
<li><b>Default:</b> <code>OFF</code></li>
<li><b>Usage:</b> Disabling the sandbox increases performance but reduces security. Only disable this option if you fully understand the security implications.</li>
</ul>
<h2><a class="anchor" id="autotoc_md22"></a>
USE_WIN_DCOMPOSITION</h2>
<ul>
<li><b>Description:</b> Enables or disables the use of Windows DirectComposition for rendering.</li>
<li><b>Values:</b> <code>ON</code> (enable) or <code>OFF</code> (disable).</li>
<li><b>Default:</b> <code>OFF</code></li>
<li><b>Usage:</b> DirectComposition can improve rendering performance on Windows. Disable this option if you encounter compatibility issues.</li>
</ul>
<h2><a class="anchor" id="autotoc_md23"></a>
USE_QT_EVENT_LOOP</h2>
<ul>
<li><b>Description:</b> Enables the use of Qt's event loop integration for CEF.</li>
<li><b>Values:</b> <code>ON</code> (enable) or <code>OFF</code> (disable).</li>
<li><b>Default:</b> <code>OFF</code></li>
<li><b>Usage:</b> This option integrates CEF's message loop with Qt's event loop, providing better responsiveness and compatibility. In rare cases, disabling this option might resolve specific event loop conflicts. </li>
</ul>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
