This project welcomes contributions and suggestions. 
Make sure to align your code style with the configuration defined by .clang-format file

Making contributions is not a hard thing. 
- Solving an issue(maybe just answering a question raised in issues list or gitter)-
- Fixing/Issuing a bug
- Improving the documents and even fixing a typo 
are important contributions to QCefView

If you would like to become one of QCefView's maintainers to contribute more (e.g. help merge PR, triage issues), please contact me by email(she<PERSON><PERSON>hen#gmail.com). I am glad to help you to set the right permission.
