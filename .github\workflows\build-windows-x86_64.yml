name: Build on Windows-x86_64

on:
  push:
    branches: [main]
    paths:
      - ".github/workflows/build-windows-x86_64.yml"
      - "CMakeLists.txt"
      - "CefViewCore"
      - "example/**"
      - "include/**"
      - "src/**"
  pull_request:
    branches: [main]
    paths:
      - ".github/workflows/build-windows-x86_64.yml"
      - "CMakeLists.txt"
      - "CefViewCore"
      - "example/**"
      - "include/**"
      - "src/**"

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:
  build:
    # The CMake configure and build commands are platform agnostic and should work equally
    # well on Windows or Mac.  You can convert this to a matrix build if you need
    # cross-platform coverage.
    # See: https://docs.github.com/en/free-pro-team@latest/actions/learn-github-actions/managing-complex-workflows#using-a-build-matrix
    runs-on: windows-latest

    steps:
      - name: Install Qt
        uses: jurplel/install-qt-action@v3

      - name: Checkout Source
        uses: actions/checkout@v2
        with:
          submodules: "true"

      - name: Cache CEF folders
        uses: actions/cache@v3
        with:
          path: ${{github.workspace}}/CefViewCore/dep
          key: ${{ runner.os }}-core-dep-cef

      - name: Configure CMake
        # Configure CMake in a 'build' subdirectory. `CMAKE_BUILD_TYPE` is only required if you are using a single-configuration generator such as make.
        # See https://cmake.org/cmake/help/latest/variable/CMAKE_BUILD_TYPE.html?highlight=cmake_build_type
        run: cmake -B ${{github.workspace}}/build -A x64 -DPROJECT_ARCH=x86_64 -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} -DBUILD_DEMO=ON -DCMAKE_INSTALL_PREFIX:PATH=${{github.workspace}}/out/install

      - name: Build
        # Build your program with the given configuration
        run: cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Install
        if: github.event_name != 'pull_request'
        # Collect the output
        run: cmake --install ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Create SDK artifact
        if: github.event_name != 'pull_request'
        uses: actions/upload-artifact@v4
        # Upload artifact
        with:
          name: QCefView.windows.x86_64
          path: ${{github.workspace}}/out/install/QCefView

      - name: Create Demo artifact
        if: github.event_name != 'pull_request'
        uses: actions/upload-artifact@v4
        # Upload artifact
        with:
          name: QCefViewTest.windows.x86_64
          path: ${{github.workspace}}/out/install/QCefViewTest

    #- name: Test
    #working-directory: ${{github.workspace}}/build
    ## Execute tests defined by the CMake configuration.
    ## See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
    #run: ctest -C ${{env.BUILD_TYPE}}
