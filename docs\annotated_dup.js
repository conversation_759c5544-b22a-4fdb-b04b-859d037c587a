var annotated_dup =
[
    [ "QCefConfig", "class_q_cef_config.html", "class_q_cef_config" ],
    [ "QCefContext", "class_q_cef_context.html", "class_q_cef_context" ],
    [ "QCefDownloadItem", "class_q_cef_download_item.html", "class_q_cef_download_item" ],
    [ "QCefEvent", "class_q_cef_event.html", "class_q_cef_event" ],
    [ "QCefQuery", "class_q_cef_query.html", "class_q_cef_query" ],
    [ "QCefSetting", "class_q_cef_setting.html", "class_q_cef_setting" ],
    [ "QCefView", "class_q_cef_view.html", "class_q_cef_view" ]
];