2025-08-05 19:39:24.588 HL-Whiteboard-Qt debug [WhiteboardView::onSideBarButtonClicked:1836] 37516:46416 - "WhiteboardView: 侧边栏按钮被点击，工具名称: 洋葱课堂，视图类型: 0，点击类型: 1，URL: https://school-test.yangcongxueyuan.com/haiLogin?from=hailiang&code=6968848006046371840，位置: (37,37)，大小: 1049x680, 缩放级别: -2.5"
2025-08-05 19:39:24.589 HL-Whiteboard-Qt debug [WhiteboardView::onSideBarButtonClicked:1857] 37516:46416 - "WhiteboardView: CEF工具被点击: 洋葱课堂"
2025-08-05 19:39:24.589 HL-Whiteboard-Qt debug [WhiteboardView::handleToolWithConfig:1894] 37516:46416 - "WhiteboardView: 使用配置信息处理工具: 洋葱课堂"
2025-08-05 19:39:24.589 HL-Whiteboard-Qt debug [WhiteboardView::handleCefViewWithConfig:1916] 37516:46416 - "WhiteboardView: 使用配置处理洋葱课堂工具，URL: https://school-test.yangcongxueyuan.com/haiLogin?from=hailiang&code=6968848006046371840，位置: (37,37)，大小: 1049x680"
2025-08-05 19:39:24.589 HL-Whiteboard-Qt info [QCefViewPrivate::createCefBrowser:122] 37516:46416 - Hardware Acceleration is disabled
2025-08-05 19:39:24.590 HL-Whiteboard-Qt info [JSBridge::init:146] 37516:46416 - QCefView loadStart 连接成功!
2025-08-05 19:39:24.597 HL-Whiteboard-Qt debug [ZIndexManager::registerComponent:67] 37516:46416 - "ZIndexManager: 注册组件 [洋葱课堂View] 到层级 3500 (类型: 2)"
2025-08-05 19:39:24.597 HL-Whiteboard-Qt debug [ZIndexManager::installActivationBlocker:586] 37516:46416 - "ZIndexManager: 为窗口 [洋葱课堂] 安装激活拦截器，允许激活: 0"
2025-08-05 19:39:24.600 HL-Whiteboard-Qt debug [WhiteboardView::handleCefViewWithConfig:2003] 37516:46416 - "WhiteboardView: 注册洋葱课堂到ZIndexManager，层级: 3500"
2025-08-05 19:39:24.609 HL-Whiteboard-Qt debug [ZIndexManager::unregisterComponent:116] 37516:46416 - "ZIndexManager: 注销组件 [洋葱课堂View]"
2025-08-05 19:39:24.609 HL-Whiteboard-Qt debug [ZIndexManager::registerComponent:67] 37516:46416 - "ZIndexManager: 注册组件 [洋葱课堂View] 到层级 3500 (类型: 2)"
2025-08-05 19:39:24.609 HL-Whiteboard-Qt debug [ZIndexManager::installActivationBlocker:586] 37516:46416 - "ZIndexManager: 为窗口 [洋葱课堂] 安装激活拦截器，允许激活: 0"
2025-08-05 19:39:24.611 HL-Whiteboard-Qt debug [WhiteboardView::handleCefViewWithConfig:2027] 37516:46416 - "WhiteboardView: 创建洋葱课堂 CefViewWidget成功 (URL: https://school-test.yangcongxueyuan.com/haiLogin?from=hailiang&code=6968848006046371840)"
[DEBUG]+++ CefViewBrowserClient::OnLoadStart
2025-08-05 19:39:25.528 HL-Whiteboard-Qt info [JSBridge::init::::operator():115] 37516:13580 - invokeMethod browserId:  3  连接成功!
2025-08-05 19:39:25.528 HL-Whiteboard-Qt info [JSBridge::init::::operator():124] 37516:13580 - cefQueryRequest browserId:  3  连接成功!
2025-08-05 19:39:25.528 HL-Whiteboard-Qt info [JSBridge::init::::operator():133] 37516:13580 - destroyed browserId:  3  连接成功!
[DEBUG]--- CefViewBrowserClient::OnLoadStart
[DEBUG]+++ CefViewBrowserClient::OnLoadEnd
2025-08-05 19:39:25.533 HL-Whiteboard-Qt debug [CefViewWidget::initLoadErrorUI::::operator():386] 37516:13580 - loadEnd: 成功加载:  0
[DEBUG]--- CefViewBrowserClient::OnLoadEnd
2025-08-05 19:39:25.533 HL-Whiteboard-Qt debug [CefViewWidget::onLoadEnd:397] 37516:46416 - CefViewWidget::onLoadEnd
[DEBUG]+++ CefViewBrowserClient::OnLoadStart
2025-08-05 19:39:25.537 HL-Whiteboard-Qt info [JSBridge::init::::operator():108] 37516:13580 - loadStart browserId:  3  已经存在，无需重复初始化!
[DEBUG]--- CefViewBrowserClient::OnLoadStart
[DEBUG]+++ CefViewBrowserClient::OnLoadEnd
2025-08-05 19:39:25.907 HL-Whiteboard-Qt debug [CefViewWidget::initLoadErrorUI::::operator():386] 37516:13580 - loadEnd: 成功加载:  200
[DEBUG]--- CefViewBrowserClient::OnLoadEnd
2025-08-05 19:39:25.908 HL-Whiteboard-Qt debug [CefViewWidget::onLoadEnd:397] 37516:46416 - CefViewWidget::onLoadEnd
[DEBUG]+++ CefViewBrowserClient::OnLoadStart
2025-08-05 19:39:26.945 HL-Whiteboard-Qt info [JSBridge::init::::operator():108] 37516:13580 - loadStart browserId:  3  已经存在，无需重复初始化!
[DEBUG]--- CefViewBrowserClient::OnLoadStart
[DEBUG]+++ CefViewBrowserClient::OnLoadEnd
2025-08-05 19:39:27.337 HL-Whiteboard-Qt debug [CefViewWidget::initLoadErrorUI::::operator():386] 37516:13580 - loadEnd: 成功加载:  200
[DEBUG]--- CefViewBrowserClient::OnLoadEnd
2025-08-05 19:39:27.338 HL-Whiteboard-Qt debug [CefViewWidget::onLoadEnd:397] 37516:46416 - CefViewWidget::onLoadEnd
2025-08-05 19:39:32.725 HL-Whiteboard-Qt debug [QCefView::mousePressEvent:488] 37516:46416 - ==== mousePressEvent: setting focus to QCefView
2025-08-05 19:39:32.888 HL-Whiteboard-Qt debug [CefViewWidget::mouseReleaseEvent:189] 37516:46416 - CefViewWidget: mouseReleaseEvent, dragging, m_isDragging: false
2025-08-05 19:39:35.565 HL-Whiteboard-Qt debug [QCefView::mousePressEvent:488] 37516:46416 - ==== mousePressEvent: setting focus to QCefView
2025-08-05 19:39:35.757 HL-Whiteboard-Qt debug [CefViewWidget::mouseReleaseEvent:189] 37516:46416 - CefViewWidget: mouseReleaseEvent, dragging, m_isDragging: false
2025-08-05 19:39:39.695 HL-Whiteboard-Qt debug [QCefView::mousePressEvent:488] 37516:46416 - ==== mousePressEvent: setting focus to QCefView
2025-08-05 19:39:39.812 HL-Whiteboard-Qt debug [CefViewWidget::mouseReleaseEvent:189] 37516:46416 - CefViewWidget: mouseReleaseEvent, dragging, m_isDragging: false
2025-08-05 19:39:41.028 HL-Whiteboard-Qt debug [QCefView::mousePressEvent:488] 37516:46416 - ==== mousePressEvent: setting focus to QCefView
2025-08-05 19:39:41.156 HL-Whiteboard-Qt debug [CefViewWidget::mouseReleaseEvent:189] 37516:46416 - CefViewWidget: mouseReleaseEvent, dragging, m_isDragging: false
onecore\net\netprofiles\service\src\nsp\dll\namespaceserviceprovider.cpp(866)\nlansp_c.dll!00007FF9B615E7C0: (caller: 00007FF9DDB23447) LogHr(1) tid(a5a8) 8007277E WSALookupServiceNext 不能返回更多的结果。
hl-whiteboard-qt.exe (37516): Loaded 'C:\Windows\System32\rasadhlp.dll'.
onecoreuap\net\netprofiles\winrt\networkinformation\lib\networkinformationprivateserver.cpp(104)\Windows.Networking.Connectivity.dll!00007FF9C10A7051: (caller: 00007FF9C10C75D4) LogHr(3) tid(a5a8) 800704C6 网络不存在或尚未启动。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(4) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(5) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(6) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(7) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(8) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(9) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(10) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(11) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(12) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(13) tid(a5a8) 800708CA 此网络连接不存在。
onecoreuap\net\netprofiles\winrt\networkinformation\lib\connectionprofileserver.cpp(406)\Windows.Networking.Connectivity.dll!00007FF9C10DB1F6: (caller: 00007FF8AFEFB300) LogHr(14) tid(a5a8) 800708CA 此网络连接不存在。
2025-08-05 19:39:56.627 HL-Whiteboard-Qt debug [QCefView::mousePressEvent:488] 37516:46416 - ==== mousePressEvent: setting focus to QCefView
2025-08-05 19:39:56.748 HL-Whiteboard-Qt debug [CefViewWidget::mouseReleaseEvent:189] 37516:46416 - CefViewWidget: mouseReleaseEvent, dragging, m_isDragging: false
2025-08-05 19:39:59.990 HL-Whiteboard-Qt debug [QCefView::mousePressEvent:488] 37516:46416 - ==== mousePressEvent: setting focus to QCefView
2025-08-05 19:40:00.098 HL-Whiteboard-Qt debug [CefViewWidget::mouseReleaseEvent:189] 37516:46416 - CefViewWidget: mouseReleaseEvent, dragging, m_isDragging: false
2025-08-05 19:40:02.576 HL-Whiteboard-Qt debug [ZIndexManager::unregisterComponent:90] 37516:46416 - "ZIndexManager: 移除widget [洋葱课堂] 的HWND映射"
2025-08-05 19:40:02.577 HL-Whiteboard-Qt debug [ZIndexManager::unregisterComponent:116] 37516:46416 - "ZIndexManager: 注销组件 [洋葱课堂View]"
2025-08-05 19:40:02.577 HL-Whiteboard-Qt debug [WhiteboardView::handleCefViewWithConfig::::operator():2013] 37516:46416 - "WhiteboardView: 从ZIndexManager注销 洋葱课堂"
2025-08-05 19:40:02.577 HL-Whiteboard-Qt debug [WhiteboardView::handleCefViewWithConfig::::operator():2017] 37516:46416 - "WhiteboardView: 洋葱课堂 CefViewWidget完全移除 (URL: https://school-test.yangcongxueyuan.com/haiLogin?from=hailiang&code=6968848006046371840)"
2025-08-05 19:40:02.594 HL-Whiteboard-Qt debug [QCefView::~QCefView:67] 37516:46416 - QCefView(0x1d15531be70) is being destructed
2025-08-05 19:40:02.594 HL-Whiteboard-Qt debug [QCefViewPrivate::destroyCefBrowser:204] 37516:46416 - destroy browser from native
2025-08-05 19:40:02.597 HL-Whiteboard-Qt debug [CCefClientDelegate::~CCefClientDelegate:16] 37516:46416 - CCefClientDelegate is being destructed
2025-08-05 19:40:02.598 HL-Whiteboard-Qt debug [JSBridge::onCefViewDestroyed:472] 37516:46416 - JSBridge onCefViewDestroyed QWidget(0x1d15531be70)
[DEBUG]CefViewBrowserClient::~CefViewBrowserClient()
