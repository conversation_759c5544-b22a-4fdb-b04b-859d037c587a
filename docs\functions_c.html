<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Class Members</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('functions_c.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all class members with links to the classes they belong to:</div>

<h3><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>cachePath()&#160;:&#160;<a class="el" href="class_q_cef_config.html#ab3a95ce139ce862abb4abb300c1cc1e3">QCefConfig</a></li>
<li>cancel()&#160;:&#160;<a class="el" href="class_q_cef_download_item.html#a723b2081749a447049efd04e768d9e57">QCefDownloadItem</a></li>
<li>cefConfig()&#160;:&#160;<a class="el" href="class_q_cef_context.html#acfd6416ebc0a8df5cf8961dadeff960e">QCefContext</a></li>
<li>cefQueryRequest()&#160;:&#160;<a class="el" href="class_q_cef_view.html#aef058b415485dba45c8dfffdcf956a5f">QCefView</a></li>
<li>cefUrlRequest()&#160;:&#160;<a class="el" href="class_q_cef_view.html#add5abd934b15c1b8b3e91703701a8cf4">QCefView</a></li>
<li>CefWindowOpenDisposition&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787">QCefView</a></li>
<li>CefWindowOpenDispositionCurrentTab&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a48d5eb6b5d1564c6668f15f1f2a45b24">QCefView</a></li>
<li>CefWindowOpenDispositionIgnoreAction&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a66fc224542ebf19b10e51aa721b1ef09">QCefView</a></li>
<li>CefWindowOpenDispositionNewBackgroundTab&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787ab43a4c561c1e83ac6419cc711220e62c">QCefView</a></li>
<li>CefWindowOpenDispositionNewForeGroundTab&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a8bd674f0da6b33d87117c7b8bf5153c2">QCefView</a></li>
<li>CefWindowOpenDispositionNewPopup&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a844b09433f6bcfe05e43fcd8c848a41d">QCefView</a></li>
<li>CefWindowOpenDispositionNewWindow&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a7b2dddad8191aff9c281badd83b710b1">QCefView</a></li>
<li>CefWindowOpenDispositionOffTheRecord&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a5e8deb29da2ecf34eaaf23f1ed7201f7">QCefView</a></li>
<li>CefWindowOpenDispositionSaveToDisk&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a664b036a79acb7f6c84c2cfdedbd4093">QCefView</a></li>
<li>CefWindowOpenDispositionSingletonTab&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a3d46df185e4a92deef33f80cfdae4c5c">QCefView</a></li>
<li>CefWindowOpenDispositionUnknown&#160;:&#160;<a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787af5284fd406b0fe65ef1071961543d3a9">QCefView</a></li>
<li>clearCrossOriginWhitelistEntry()&#160;:&#160;<a class="el" href="class_q_cef_context.html#a95d71c83fef34e8218a8ce559f173ab4">QCefContext</a></li>
<li>closeDevTools()&#160;:&#160;<a class="el" href="class_q_cef_view.html#abdf0a68139fe9163ecd9b5a0cdeed6d7">QCefView</a></li>
<li>commandLinePassthroughDisabled()&#160;:&#160;<a class="el" href="class_q_cef_config.html#a892715bebd8dc71e5acb0be17bfff43d">QCefConfig</a></li>
<li>consoleMessage()&#160;:&#160;<a class="el" href="class_q_cef_view.html#a2dae6946082712815273c2967d37762a">QCefView</a></li>
<li>contentDisposition()&#160;:&#160;<a class="el" href="class_q_cef_download_item.html#af97dad25e439c8b997d6689fe1c91bf8">QCefDownloadItem</a></li>
<li>contextMenuEvent()&#160;:&#160;<a class="el" href="class_q_cef_view.html#ac8a83d1f2fb0e771fb48007838b40d1f">QCefView</a></li>
<li>currentSpeed()&#160;:&#160;<a class="el" href="class_q_cef_download_item.html#a5c9648906b02ce59aa6d82f03e468c1d">QCefDownloadItem</a></li>
<li>cursiveFontFamily()&#160;:&#160;<a class="el" href="class_q_cef_setting.html#a86526505941342ffbc96b6e5226ffcbe">QCefSetting</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
