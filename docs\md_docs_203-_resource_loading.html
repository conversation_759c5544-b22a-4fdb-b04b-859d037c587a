<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Resource Loading</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('md_docs_203-_resource_loading.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Resource Loading</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul>
  <li class="level1">
    <a href="#autotoc_md30">1. Load Online Web Resource</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md31">2. Load Local File With File Path</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md32">3. Add Local Folder Mapping</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md33">4. Add Local Archive Mapping</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md34">Use Cases</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md35">Summary</a>
  </li>
</ul>
</div>
<div class="textblock"><p><a class="anchor" id="autotoc_md29"></a></p>
<p>To load the web resource, <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> provide 4 methods to achieve it.</p>
<h1><a class="anchor" id="autotoc_md30"></a>
1. Load Online Web Resource</h1>
<p>This method allows you to load web resources directly from the internet by specifying a URL. It's the simplest way to display online content within your <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>.</p>
<p>You can pass the full URL of the webpage to the constructor of <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>. </p><div class="fragment"><div class="line"><span class="comment">// build settings for per QCefView</span></div>
<div class="line"><a class="code hl_class" href="class_q_cef_setting.html">QCefSetting</a> setting;</div>
<div class="line"> </div>
<div class="line"><span class="comment">// create the QCefView widget and add it to the layout container</span></div>
<div class="line"><a class="code hl_class" href="class_q_cef_view.html">QCefView</a>* cefView = <span class="keyword">new</span> <a class="code hl_class" href="class_q_cef_view.html">QCefView</a>(<span class="stringliteral">&quot;https://google.com&quot;</span>, &amp;setting, <span class="keyword">nullptr</span>);</div>
<div class="ttc" id="aclass_q_cef_setting_html"><div class="ttname"><a href="class_q_cef_setting.html">QCefSetting</a></div><div class="ttdoc">Represents the settings for individual browser.</div><div class="ttdef"><b>Definition</b> QCefSetting.h:27</div></div>
<div class="ttc" id="aclass_q_cef_view_html"><div class="ttname"><a href="class_q_cef_view.html">QCefView</a></div><div class="ttdoc">Represents the CEF browser view.</div><div class="ttdef"><b>Definition</b> QCefView.h:49</div></div>
</div><!-- fragment --><h1><a class="anchor" id="autotoc_md31"></a>
2. Load Local File With File Path</h1>
<p>This method enables you to load a local HTML file into the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>. It's useful for displaying static content or for testing web applications locally.</p>
<p>Pass the absolute file path of the web resource file to the constructor of <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>. Note the file path must be started by "file://" schema. </p><div class="fragment"><div class="line"><span class="comment">// build the path to the web resource</span></div>
<div class="line">QDir dir = QCoreApplication::applicationDirPath();</div>
<div class="line">QString webResourceDir = QString(<span class="stringliteral">&quot;file://&quot;</span>) + QDir::toNativeSeparators(dir.filePath(<span class="stringliteral">&quot;webres/index.html&quot;</span>));</div>
<div class="line"> </div>
<div class="line"><span class="comment">// build settings for per QCefView</span></div>
<div class="line"><a class="code hl_class" href="class_q_cef_setting.html">QCefSetting</a> setting;</div>
<div class="line"> </div>
<div class="line"><span class="comment">// create the QCefView widget and add it to the layout container</span></div>
<div class="line"><a class="code hl_class" href="class_q_cef_view.html">QCefView</a>* cefView = <span class="keyword">new</span> <a class="code hl_class" href="class_q_cef_view.html">QCefView</a>(INDEX_URL, &amp;setting, <span class="keyword">nullptr</span>);</div>
</div><!-- fragment --> <h1><a class="anchor" id="autotoc_md32"></a>
3. Add Local Folder Mapping</h1>
<p>This method is designed for loading entire web application folders. It maps a local directory to a URL, allowing you to access files within that directory as if they were hosted on a web server. This is particularly useful for Single Page Applications (SPAs) and web applications with complex directory structures.</p>
<ul>
<li><code>public void <a class="el" href="class_q_cef_context.html#aecc6f7ee9d296bcf8d2ba470e0c0e454" title="Adds a url mapping item with local web resource directory. This works for all  instances created subs...">QCefContext::addLocalFolderResource</a>(const QString &amp; path,const QString &amp; url,int priority)</code></li>
<li><code>public void <a class="el" href="class_q_cef_view.html#a8c6286b279094a691832fc89b93c75f1" title="Adds a url mapping item with local web resource directory.">QCefView::addLocalFolderResource</a>(const QString &amp; path,const QString &amp; url,int priority)</code></li>
</ul>
<p>For example, you build the WebApp project and get the output folder <code>webres</code>, the folder structure is as follows: </p><div class="fragment"><div class="line">full\path\to\webres</div>
<div class="line">                │   index.html</div>
<div class="line">                ├───assets</div>
<div class="line">                ├───docs</div>
<div class="line">                ├───img</div>
</div><!-- fragment --><p>You can add a mapping item with the following code: </p><div class="fragment"><div class="line"><span class="comment">// add a local folder to URL map</span></div>
<div class="line"> <a class="code hl_function" href="class_q_cef_context.html#a3e6491f837fdd72c7b4fefed5569853b">QCefContext::instance</a>()-&gt;<a class="code hl_function" href="class_q_cef_context.html#aecc6f7ee9d296bcf8d2ba470e0c0e454">addLocalFolderResource</a>(</div>
<div class="line">     <span class="stringliteral">&quot;full\\path\\to\\webres&quot;</span>, </div>
<div class="line">     <span class="stringliteral">&quot;https://domainname&quot;</span>              <span class="comment">// This could be any URL you need</span></div>
<div class="line">     );</div>
<div class="line"> </div>
<div class="line"> <span class="comment">// build settings for per QCefView</span></div>
<div class="line"> <a class="code hl_class" href="class_q_cef_setting.html">QCefSetting</a> setting;</div>
<div class="line"> </div>
<div class="line"> <span class="comment">// create the QCefView widget and add it to the layout container</span></div>
<div class="line"> <a class="code hl_class" href="class_q_cef_view.html">QCefView</a>* cefView = <span class="keyword">new</span> <a class="code hl_class" href="class_q_cef_view.html">QCefView</a>(</div>
<div class="line">     <span class="stringliteral">&quot;https://domainname/index.html&quot;</span>, </div>
<div class="line">     &amp;setting, </div>
<div class="line">     <span class="keyword">this</span></div>
<div class="line">     );</div>
<div class="ttc" id="aclass_q_cef_context_html_a3e6491f837fdd72c7b4fefed5569853b"><div class="ttname"><a href="class_q_cef_context.html#a3e6491f837fdd72c7b4fefed5569853b">QCefContext::instance</a></div><div class="ttdeci">static QCefContext * instance()</div><div class="ttdoc">Gets the unique default instance.</div></div>
<div class="ttc" id="aclass_q_cef_context_html_aecc6f7ee9d296bcf8d2ba470e0c0e454"><div class="ttname"><a href="class_q_cef_context.html#aecc6f7ee9d296bcf8d2ba470e0c0e454">QCefContext::addLocalFolderResource</a></div><div class="ttdeci">void addLocalFolderResource(const QString &amp;path, const QString &amp;url, int priority=0)</div><div class="ttdoc">Adds a url mapping item with local web resource directory. This works for all  instances created subs...</div></div>
</div><!-- fragment --><p>After added the mapping item, you can access all the resource with the URL root appended by the resource relative path.</p>
<h1><a class="anchor" id="autotoc_md33"></a>
4. Add Local Archive Mapping</h1>
<p>This method allows you to load web resources from a ZIP archive. It maps a URL to a ZIP file, enabling you to serve the contents of the archive as if they were a web server directory. This is useful for distributing web applications as a single file or for loading resources from compressed archives.</p>
<ul>
<li><code>public void <a class="el" href="class_q_cef_context.html#aba9c70a84379190d151bdc4b634367e6" title="Adds a url mapping item with local archive (.zip) file which contains the web resource....">QCefContext::addArchiveResource</a>(const QString &amp; path,const QString &amp; url,const QString &amp; password)</code></li>
<li><code>public void <a class="el" href="class_q_cef_view.html#a503148f8ff5ca5b28d3f0e123bf5bf76" title="Adds a url mapping item with local archive (.zip) file which contains the web resource.">QCefView::addArchiveResource</a>(const QString &amp; path,const QString &amp; url,const QString &amp; password)</code></li>
</ul>
<p>You need to keep the folder structure in the archive file, for example: </p><div class="fragment"><div class="line">full\path\to\webres.zip</div>
<div class="line">                │   index.html</div>
<div class="line">                ├───assets</div>
<div class="line">                ├───docs</div>
<div class="line">                ├───img</div>
</div><!-- fragment --><p>You can add a mapping item with the following code: </p><div class="fragment"><div class="line"><span class="comment">// add a local zip file to URL map</span></div>
<div class="line"><a class="code hl_function" href="class_q_cef_context.html#a3e6491f837fdd72c7b4fefed5569853b">QCefContext::instance</a>()-&gt;<a class="code hl_function" href="class_q_cef_context.html#aba9c70a84379190d151bdc4b634367e6">addArchiveResource</a>(</div>
<div class="line">    <span class="stringliteral">&quot;full\\path\\to\\webres.zip&quot;</span>, </div>
<div class="line">    <span class="stringliteral">&quot;https://domainname&quot;</span>,</div>
<div class="line">    <span class="stringliteral">&quot;password&quot;</span>                 <span class="comment">// pass the password of the zip file if needed</span></div>
<div class="line">    );</div>
<div class="line"> </div>
<div class="line"> <span class="comment">// build settings for per QCefView</span></div>
<div class="line"> <a class="code hl_class" href="class_q_cef_setting.html">QCefSetting</a> setting;</div>
<div class="line"> </div>
<div class="line"> <span class="comment">// create the QCefView widget and add it to the layout container</span></div>
<div class="line"> <a class="code hl_class" href="class_q_cef_view.html">QCefView</a>* cefView = <span class="keyword">new</span> <a class="code hl_class" href="class_q_cef_view.html">QCefView</a>(</div>
<div class="line">     <span class="stringliteral">&quot;https://domainname/index.html&quot;</span>, </div>
<div class="line">     &amp;setting, </div>
<div class="line">     <span class="keyword">this</span></div>
<div class="line">     );</div>
<div class="ttc" id="aclass_q_cef_context_html_aba9c70a84379190d151bdc4b634367e6"><div class="ttname"><a href="class_q_cef_context.html#aba9c70a84379190d151bdc4b634367e6">QCefContext::addArchiveResource</a></div><div class="ttdeci">void addArchiveResource(const QString &amp;path, const QString &amp;url, const QString &amp;password=&quot;&quot;, int priority=0)</div><div class="ttdoc">Adds a url mapping item with local archive (.zip) file which contains the web resource....</div></div>
</div><!-- fragment --><h1><a class="anchor" id="autotoc_md34"></a>
Use Cases</h1>
<p>The above 3 &amp; 4 are very useful when you develop your WebApp with the popular framework like React, Vue or some others, especially you are building SPA WebApp.</p>
<h1><a class="anchor" id="autotoc_md35"></a>
Summary</h1>
<p>These four methods provide flexible options for loading web resources into <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>, catering to different scenarios from online content to local files and complex web applications. Choose the method that best suits your needs based on the source and structure of your web resources. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
