<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: QCefContext</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_q_cef_context.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="class_q_cef_context-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">QCefContext</div></div>
</div><!--header-->
<div class="contents">

<p>Represents the CEF context.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;QCefContext.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa2b8af8d2d806ba8b5110e868d314c8c" id="r_aa2b8af8d2d806ba8b5110e868d314c8c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa2b8af8d2d806ba8b5110e868d314c8c">QCefContext</a> (QCoreApplication *app, int argc, char **argv, const <a class="el" href="class_q_cef_config.html">QCefConfig</a> *config)</td></tr>
<tr class="memdesc:aa2b8af8d2d806ba8b5110e868d314c8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs the CEF context.  <br /></td></tr>
<tr class="separator:aa2b8af8d2d806ba8b5110e868d314c8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91de7d9c36aafdaca390a355d6da5c6d" id="r_a91de7d9c36aafdaca390a355d6da5c6d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a91de7d9c36aafdaca390a355d6da5c6d">~QCefContext</a> ()</td></tr>
<tr class="memdesc:a91de7d9c36aafdaca390a355d6da5c6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destructs the CEF context.  <br /></td></tr>
<tr class="separator:a91de7d9c36aafdaca390a355d6da5c6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecc6f7ee9d296bcf8d2ba470e0c0e454" id="r_aecc6f7ee9d296bcf8d2ba470e0c0e454"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aecc6f7ee9d296bcf8d2ba470e0c0e454">addLocalFolderResource</a> (const QString &amp;path, const QString &amp;url, int priority=0)</td></tr>
<tr class="memdesc:aecc6f7ee9d296bcf8d2ba470e0c0e454"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a url mapping item with local web resource directory. This works for all  instances created subsequently.  <br /></td></tr>
<tr class="separator:aecc6f7ee9d296bcf8d2ba470e0c0e454"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba9c70a84379190d151bdc4b634367e6" id="r_aba9c70a84379190d151bdc4b634367e6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aba9c70a84379190d151bdc4b634367e6">addArchiveResource</a> (const QString &amp;path, const QString &amp;url, const QString &amp;password=&quot;&quot;, int priority=0)</td></tr>
<tr class="memdesc:aba9c70a84379190d151bdc4b634367e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a url mapping item with local archive (.zip) file which contains the web resource. This works for all  instances created subsequently.  <br /></td></tr>
<tr class="separator:aba9c70a84379190d151bdc4b634367e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d2e90de7fb5fcf2b7e7a6581d26e62c" id="r_a6d2e90de7fb5fcf2b7e7a6581d26e62c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6d2e90de7fb5fcf2b7e7a6581d26e62c">addCookie</a> (const QString &amp;name, const QString &amp;value, const QString &amp;domain, const QString &amp;url)</td></tr>
<tr class="memdesc:a6d2e90de7fb5fcf2b7e7a6581d26e62c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a cookie to the CEF context, this cookie is accessible from all browsers created with this context.  <br /></td></tr>
<tr class="separator:a6d2e90de7fb5fcf2b7e7a6581d26e62c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a794e41a74ddabc503bed5e8c47fe3dd0" id="r_a794e41a74ddabc503bed5e8c47fe3dd0"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a794e41a74ddabc503bed5e8c47fe3dd0">deleteAllCookies</a> ()</td></tr>
<tr class="memdesc:a794e41a74ddabc503bed5e8c47fe3dd0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Deletes all cookies from the CEF context.  <br /></td></tr>
<tr class="separator:a794e41a74ddabc503bed5e8c47fe3dd0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaba077228a77f5e7d7491eda3ce10267" id="r_aaba077228a77f5e7d7491eda3ce10267"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaba077228a77f5e7d7491eda3ce10267">addCrossOriginWhitelistEntry</a> (const QString &amp;sourceOrigin, const QString &amp;targetSchema, const QString &amp;targetDomain, bool allowTargetSubdomains)</td></tr>
<tr class="memdesc:aaba077228a77f5e7d7491eda3ce10267"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds an entry to the cross-origin access whitelist. For details please refer to: <a href="https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L81C23-L81C23">https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L81C23-L81C23</a>.  <br /></td></tr>
<tr class="separator:aaba077228a77f5e7d7491eda3ce10267"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af04aefeea503031f5a8fbdabf05bc5e8" id="r_af04aefeea503031f5a8fbdabf05bc5e8"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af04aefeea503031f5a8fbdabf05bc5e8">removeCrossOriginWhitelistEntry</a> (const QString &amp;sourceOrigin, const QString &amp;targetSchema, const QString &amp;targetDomain, bool allowTargetSubdomains)</td></tr>
<tr class="memdesc:af04aefeea503031f5a8fbdabf05bc5e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Removes an entry from the cross-origin access whitelist. For details please refer to: <a href="https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L91C12-L91C12">https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L91C12-L91C12</a>.  <br /></td></tr>
<tr class="separator:af04aefeea503031f5a8fbdabf05bc5e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95d71c83fef34e8218a8ce559f173ab4" id="r_a95d71c83fef34e8218a8ce559f173ab4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a95d71c83fef34e8218a8ce559f173ab4">clearCrossOriginWhitelistEntry</a> ()</td></tr>
<tr class="memdesc:a95d71c83fef34e8218a8ce559f173ab4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Removes all entries from the cross-origin access whitelist.  <br /></td></tr>
<tr class="separator:a95d71c83fef34e8218a8ce559f173ab4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acfd6416ebc0a8df5cf8961dadeff960e" id="r_acfd6416ebc0a8df5cf8961dadeff960e"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="class_q_cef_config.html">QCefConfig</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acfd6416ebc0a8df5cf8961dadeff960e">cefConfig</a> () const</td></tr>
<tr class="memdesc:acfd6416ebc0a8df5cf8961dadeff960e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the <a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a>.  <br /></td></tr>
<tr class="separator:acfd6416ebc0a8df5cf8961dadeff960e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a3e6491f837fdd72c7b4fefed5569853b" id="r_a3e6491f837fdd72c7b4fefed5569853b"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_q_cef_context.html">QCefContext</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3e6491f837fdd72c7b4fefed5569853b">instance</a> ()</td></tr>
<tr class="memdesc:a3e6491f837fdd72c7b4fefed5569853b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the unique default instance.  <br /></td></tr>
<tr class="separator:a3e6491f837fdd72c7b4fefed5569853b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pro-methods" name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a78836c8d4d2bdf4970a256d8d29c80c6" id="r_a78836c8d4d2bdf4970a256d8d29c80c6"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a78836c8d4d2bdf4970a256d8d29c80c6">init</a> (const <a class="el" href="class_q_cef_config.html">QCefConfig</a> *config)</td></tr>
<tr class="memdesc:a78836c8d4d2bdf4970a256d8d29c80c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize the CEF context.  <br /></td></tr>
<tr class="separator:a78836c8d4d2bdf4970a256d8d29c80c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee74a7460786ddc17f8f9c0f68eaab6b" id="r_aee74a7460786ddc17f8f9c0f68eaab6b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aee74a7460786ddc17f8f9c0f68eaab6b">uninit</a> ()</td></tr>
<tr class="memdesc:aee74a7460786ddc17f8f9c0f68eaab6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uninitialize the CEF context.  <br /></td></tr>
<tr class="separator:aee74a7460786ddc17f8f9c0f68eaab6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Represents the CEF context. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="aa2b8af8d2d806ba8b5110e868d314c8c" name="aa2b8af8d2d806ba8b5110e868d314c8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa2b8af8d2d806ba8b5110e868d314c8c">&#9670;&#160;</a></span>QCefContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QCefContext::QCefContext </td>
          <td>(</td>
          <td class="paramtype">QCoreApplication *</td>          <td class="paramname"><span class="paramname"><em>app</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>argc</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char **</td>          <td class="paramname"><span class="paramname"><em>argv</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="class_q_cef_config.html">QCefConfig</a> *</td>          <td class="paramname"><span class="paramname"><em>config</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructs the CEF context. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">app</td><td>The application</td></tr>
    <tr><td class="paramname">argc</td><td>The argument count</td></tr>
    <tr><td class="paramname">argv</td><td>The argument list pointer</td></tr>
    <tr><td class="paramname">config</td><td>The <a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a> instance</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a91de7d9c36aafdaca390a355d6da5c6d" name="a91de7d9c36aafdaca390a355d6da5c6d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91de7d9c36aafdaca390a355d6da5c6d">&#9670;&#160;</a></span>~QCefContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QCefContext::~QCefContext </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Destructs the CEF context. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aba9c70a84379190d151bdc4b634367e6" name="aba9c70a84379190d151bdc4b634367e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba9c70a84379190d151bdc4b634367e6">&#9670;&#160;</a></span>addArchiveResource()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QCefContext::addArchiveResource </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>path</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>url</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>password</em></span><span class="paramdefsep"> = </span><span class="paramdefval">&quot;&quot;</span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>priority</em></span><span class="paramdefsep"> = </span><span class="paramdefval">0</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a url mapping item with local archive (.zip) file which contains the web resource. This works for all  instances created subsequently. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">path</td><td>The path to the local archive file</td></tr>
    <tr><td class="paramname">url</td><td>The url to be mapped to</td></tr>
    <tr><td class="paramname">password</td><td>The password of the archive</td></tr>
    <tr><td class="paramname">priority</td><td>The priority</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6d2e90de7fb5fcf2b7e7a6581d26e62c" name="a6d2e90de7fb5fcf2b7e7a6581d26e62c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6d2e90de7fb5fcf2b7e7a6581d26e62c">&#9670;&#160;</a></span>addCookie()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QCefContext::addCookie </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>name</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>domain</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>url</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a cookie to the CEF context, this cookie is accessible from all browsers created with this context. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>The cookie item name</td></tr>
    <tr><td class="paramname">value</td><td>The cookie item value</td></tr>
    <tr><td class="paramname">domain</td><td>The applicable domain name</td></tr>
    <tr><td class="paramname">url</td><td>The applicable url</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>True on success; otherwise false</dd></dl>

</div>
</div>
<a id="aaba077228a77f5e7d7491eda3ce10267" name="aaba077228a77f5e7d7491eda3ce10267"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaba077228a77f5e7d7491eda3ce10267">&#9670;&#160;</a></span>addCrossOriginWhitelistEntry()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QCefContext::addCrossOriginWhitelistEntry </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>sourceOrigin</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>targetSchema</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>targetDomain</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool</td>          <td class="paramname"><span class="paramname"><em>allowTargetSubdomains</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds an entry to the cross-origin access whitelist. For details please refer to: <a href="https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L81C23-L81C23">https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L81C23-L81C23</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sourceOrigin</td><td>The source origin</td></tr>
    <tr><td class="paramname">targetSchema</td><td>The target schema</td></tr>
    <tr><td class="paramname">targetDomain</td><td>The target domain</td></tr>
    <tr><td class="paramname">allowTargetSubdomains</td><td>Whether to allow subdomain or not</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>True on success; otherwise false</dd></dl>

</div>
</div>
<a id="aecc6f7ee9d296bcf8d2ba470e0c0e454" name="aecc6f7ee9d296bcf8d2ba470e0c0e454"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aecc6f7ee9d296bcf8d2ba470e0c0e454">&#9670;&#160;</a></span>addLocalFolderResource()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QCefContext::addLocalFolderResource </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>path</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>url</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int</td>          <td class="paramname"><span class="paramname"><em>priority</em></span><span class="paramdefsep"> = </span><span class="paramdefval">0</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a url mapping item with local web resource directory. This works for all  instances created subsequently. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">path</td><td>The path to the local resource directory</td></tr>
    <tr><td class="paramname">url</td><td>The url to be mapped to</td></tr>
    <tr><td class="paramname">priority</td><td>The priority</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="acfd6416ebc0a8df5cf8961dadeff960e" name="acfd6416ebc0a8df5cf8961dadeff960e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acfd6416ebc0a8df5cf8961dadeff960e">&#9670;&#160;</a></span>cefConfig()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="class_q_cef_config.html">QCefConfig</a> * QCefContext::cefConfig </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets the <a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a>. </p>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a> instance</dd></dl>

</div>
</div>
<a id="a95d71c83fef34e8218a8ce559f173ab4" name="a95d71c83fef34e8218a8ce559f173ab4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95d71c83fef34e8218a8ce559f173ab4">&#9670;&#160;</a></span>clearCrossOriginWhitelistEntry()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QCefContext::clearCrossOriginWhitelistEntry </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Removes all entries from the cross-origin access whitelist. </p>

</div>
</div>
<a id="a794e41a74ddabc503bed5e8c47fe3dd0" name="a794e41a74ddabc503bed5e8c47fe3dd0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a794e41a74ddabc503bed5e8c47fe3dd0">&#9670;&#160;</a></span>deleteAllCookies()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QCefContext::deleteAllCookies </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Deletes all cookies from the CEF context. </p>
<dl class="section return"><dt>Returns</dt><dd>True on success; otherwise false</dd></dl>

</div>
</div>
<a id="a78836c8d4d2bdf4970a256d8d29c80c6" name="a78836c8d4d2bdf4970a256d8d29c80c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78836c8d4d2bdf4970a256d8d29c80c6">&#9670;&#160;</a></span>init()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QCefContext::init </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="class_q_cef_config.html">QCefConfig</a> *</td>          <td class="paramname"><span class="paramname"><em>config</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel protected">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize the CEF context. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">config</td><td>The <a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a> instance</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>True on success; otherwise false</dd></dl>

</div>
</div>
<a id="a3e6491f837fdd72c7b4fefed5569853b" name="a3e6491f837fdd72c7b4fefed5569853b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e6491f837fdd72c7b4fefed5569853b">&#9670;&#160;</a></span>instance()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_q_cef_context.html">QCefContext</a> * QCefContext::instance </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Gets the unique default instance. </p>
<dl class="section return"><dt>Returns</dt><dd>The default instance</dd></dl>

</div>
</div>
<a id="af04aefeea503031f5a8fbdabf05bc5e8" name="af04aefeea503031f5a8fbdabf05bc5e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af04aefeea503031f5a8fbdabf05bc5e8">&#9670;&#160;</a></span>removeCrossOriginWhitelistEntry()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QCefContext::removeCrossOriginWhitelistEntry </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>sourceOrigin</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>targetSchema</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>targetDomain</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool</td>          <td class="paramname"><span class="paramname"><em>allowTargetSubdomains</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Removes an entry from the cross-origin access whitelist. For details please refer to: <a href="https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L91C12-L91C12">https://github.com/chromiumembedded/cef/blob/605c2bac86415dcec1e2902cdc46dc11c1ad026a/include/cef_origin_whitelist.h#L91C12-L91C12</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sourceOrigin</td><td>The source origin</td></tr>
    <tr><td class="paramname">targetSchema</td><td>The target schema</td></tr>
    <tr><td class="paramname">targetDomain</td><td>The target domain</td></tr>
    <tr><td class="paramname">allowTargetSubdomains</td><td>Whether to allow subdomain or not</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>True on success; otherwise false</dd></dl>

</div>
</div>
<a id="aee74a7460786ddc17f8f9c0f68eaab6b" name="aee74a7460786ddc17f8f9c0f68eaab6b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee74a7460786ddc17f8f9c0f68eaab6b">&#9670;&#160;</a></span>uninit()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QCefContext::uninit </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel protected">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Uninitialize the CEF context. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_q_cef_context.html">QCefContext</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
