.github-corner svg {
    fill: var(--primary-light-color);
    color: var(--page-background-color);
    width: 72px;
    height: 72px;
}

@media screen and (max-width: 767px) {
    .github-corner svg {
        width: 50px;
        height: 50px;
    }
    #projectnumber {
        margin-right: 22px;
    }
}

.alter-theme-button {
    display: inline-block;
    cursor: pointer;
    background: var(--primary-color);
    color: var(--page-background-color) !important;
    border-radius: var(--border-radius-medium);
    padding: var(--spacing-small) var(--spacing-medium);
    text-decoration: none;
}

.alter-theme-button:hover {
    background: var(--primary-dark-color);
}

html.dark-mode .darkmode_inverted_image img, /* < doxygen 1.9.3 */
html.dark-mode .darkmode_inverted_image object[type="image/svg+xml"] /* doxygen 1.9.3 */ {
    filter: brightness(89%) hue-rotate(180deg) invert();
}

.bordered_image {
    border-radius: var(--border-radius-small);
    border: 1px solid var(--separator-color);
    display: inline-block;
    overflow: hidden;
}

html.dark-mode .bordered_image img, /* < doxygen 1.9.3 */
html.dark-mode .bordered_image object[type="image/svg+xml"] /* doxygen 1.9.3 */ {
    border-radius: var(--border-radius-small);
}

.title_screenshot {
    filter: drop-shadow(0px 3px 10px rgba(0,0,0,0.22));
    max-width: 500px;
    margin: var(--spacing-large) 0;
}

.title_screenshot .caption {
    display: none;
}
