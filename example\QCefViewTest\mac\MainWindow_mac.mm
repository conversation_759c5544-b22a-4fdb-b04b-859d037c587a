#include "MainWindow.h"
#include <Cocoa/Cocoa.h>

void
MainWindow::setupWindow() {
    // [NSApp setAppearance:[NSAppearance appearanceNamed:NSAppearanceNameAqua]];
    
    // id hwnd = (id)this->winId();
    // NSWindow* window = [hwnd window];
    // window.titlebarAppearsTransparent = true;
    // [window setTitle: @""];
    // window.movableByWindowBackground = true;
    // window.styleMask |= NSWindowStyleMaskFullSizeContentView;
}
