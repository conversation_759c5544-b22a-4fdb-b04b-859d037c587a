/*---------------- Search Box positioning */

#main-menu > li:last-child {
    /* This <li> object is the parent of the search bar */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 36px;
    margin-right: 1em;
}

/*---------------- Search box styling */

.SRPage * {
    font-weight: normal;
    line-height: normal;
}

dark-mode-toggle {
    margin-left: 5px;
    display: flex;
    float: right;
}

#MSearchBox {
    display: inline-block;
    white-space : nowrap;
    background: var(--search-background-color);
    border-radius: 0.65em;
    box-shadow: var(--search-box-shadow);
    z-index: 102;
}

#MSearchBox .left {
    display: inline-block;
    vertical-align: middle;
    height: 1.4em;
}

#MSearchSelect {
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 19px;
    background-image: var(--search-magnification-select-image);
    margin: 0 0 0 0.3em;
    padding: 0;
}

#MSearchSelectExt {
    display: inline-block;
    vertical-align: middle;
    width: 10px;
    height: 19px;
    background-image: var(--search-magnification-image);
    margin: 0 0 0 0.5em;
    padding: 0;
}


#MSearchField {
    display: inline-block;
    vertical-align: middle;
    width: 7.5em;
    height: 19px;
    margin: 0 0.15em;
    padding: 0;
    line-height: 1em;
    border:none;
    color: var(--search-foreground-color);
    outline: none;
    font-family: var(--font-family-search);
    -webkit-border-radius: 0px;
    border-radius: 0px;
    background: none;
}

@media(hover: none) {
    /* to avoid zooming on iOS */
    #MSearchField {
        font-size: 16px;
    }
}

#MSearchBox .right {
    display: inline-block;
    vertical-align: middle;
    width: 1.4em;
    height: 1.4em;
}

#MSearchClose {
    display: none;
    font-size: inherit;
    background : none;
    border: none;
    margin: 0;
    padding: 0;
    outline: none;

}

#MSearchCloseImg {
    padding: 0.3em;
    margin: 0;
}

.MSearchBoxActive #MSearchField {
    color: var(--search-active-color);
}



/*---------------- Search filter selection */

#MSearchSelectWindow {
    display: none;
    position: absolute;
    left: 0; top: 0;
    border: 1px solid var(--search-filter-border-color);
    background-color: var(--search-filter-background-color);
    z-index: 10001;
    padding-top: 4px;
    padding-bottom: 4px;
    -moz-border-radius: 4px;
    -webkit-border-top-left-radius: 4px;
    -webkit-border-top-right-radius: 4px;
    -webkit-border-bottom-left-radius: 4px;
    -webkit-border-bottom-right-radius: 4px;
    -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
}

.SelectItem {
    font: 8pt var(--font-family-search);
    padding-left:  2px;
    padding-right: 12px;
    border: 0px;
}

span.SelectionMark {
    margin-right: 4px;
    font-family: var(--font-family-monospace);
    outline-style: none;
    text-decoration: none;
}

a.SelectItem {
    display: block;
    outline-style: none;
    color: var(--search-filter-foreground-color);
    text-decoration: none;
    padding-left:   6px;
    padding-right: 12px;
}

a.SelectItem:focus,
a.SelectItem:active {
    color: var(--search-filter-foreground-color);
    outline-style: none;
    text-decoration: none;
}

a.SelectItem:hover {
    color: var(--search-filter-highlight-text-color);
    background-color: var(--search-filter-highlight-bg-color);
    outline-style: none;
    text-decoration: none;
    cursor: pointer;
    display: block;
}

/*---------------- Search results window */

iframe#MSearchResults {
    /*width: 60ex;*/
    height: 15em;
}

#MSearchResultsWindow {
    display: none;
    position: absolute;
    left: 0; top: 0;
    border: 1px solid var(--search-results-border-color);
    background-color: var(--search-results-background-color);
    z-index:10000;
    width: 300px;
    height: 400px;
    overflow: auto;
}

/* ----------------------------------- */


#SRIndex {
    clear:both; 
}

.SREntry {
    font-size: 10pt;
    padding-left: 1ex;
}

.SRPage .SREntry {
    font-size: 8pt;
    padding: 1px 5px;
}

div.SRPage {
    margin: 5px 2px;
    background-color: var(--search-results-background-color);
}

.SRChildren {
    padding-left: 3ex; padding-bottom: .5em 
}

.SRPage .SRChildren {
    display: none;
}

.SRSymbol {
    font-weight: bold;
    color: var(--search-results-foreground-color);
    font-family: var(--font-family-search);
    text-decoration: none;
    outline: none;
}

a.SRScope {
    display: block;
    color: var(--search-results-foreground-color);
    font-family: var(--font-family-search);
    font-size: 8pt;
    text-decoration: none;
    outline: none;
}

a.SRSymbol:focus, a.SRSymbol:active,
a.SRScope:focus, a.SRScope:active {
    text-decoration: underline;
}

span.SRScope {
    padding-left: 4px;
    font-family: var(--font-family-search);
}

.SRPage .SRStatus {
    padding: 2px 5px;
    font-size: 8pt;
    font-style: italic;
    font-family: var(--font-family-search);
}

.SRResult {
    display: none;
}

div.searchresults {
    margin-left: 10px;
    margin-right: 10px;
}

/*---------------- External search page results */

.pages b {
   color: white;
   padding: 5px 5px 3px 5px;
   background-image: var(--nav-gradient-active-image-parent);
   background-repeat: repeat-x;
   text-shadow: 0 1px 1px #000000;
}

.pages {
    line-height: 17px;
    margin-left: 4px;
    text-decoration: none;
}

.hl {
    font-weight: bold;
}

#searchresults {
    margin-bottom: 20px;
}

.searchpages {
    margin-top: 10px;
}

