﻿#include "details/CCefClientDelegate.h"

#include <QDebug>
#include <QSharedPointer>
#include <QThread>

#include "details/QCefViewPrivate.h"
#include "details/utils/CommonUtils.h"
#include "details/utils/ValueConvertor.h"

bool
CCefClientDelegate::onFileDialog(CefRefPtr<CefBrowser>& browser,
                                 CefBrowserHost::FileDialogMode mode,
                                 const CefString& title,
                                 const CefString& default_file_path,
                                 const std::vector<CefString>& accept_filters,
#if CEF_VERSION_MAJOR < 102
                                 int selected_accept_filter,
#endif
                                 CefRefPtr<CefFileDialogCallback>& callback)
{
  QMetaObject::invokeMethod(pCefViewPrivate_, [=]() {
    QStringList filters;
    if (!accept_filters.empty()) {
      QStringList extensions;
      QString description = "Accepted files";

      for (const auto& filter : accept_filters) {
        QString filterStr = QString::fromStdString(filter.ToString());

        if (filterStr.startsWith(".")) {
          extensions << "*" + filterStr;
        } else if (filterStr.contains("/")) {
          if (filterStr == "image/*") {
            extensions << "*.jpg" << "*.jpeg" << "*.png" << "*.gif" << "*.bmp" << "*.svg" << "*.webp";
            description = "Image files";
          } else if (filterStr == "audio/*") {
            extensions << "*.mp3" << "*.wav" << "*.ogg" << "*.m4a" << "*.aac" << "*.flac";
            description = "Audio files";
          } else if (filterStr == "video/*") {
            extensions << "*.mp4" << "*.avi" << "*.mov" << "*.wmv" << "*.flv" << "*.webm" << "*.mkv";
            description = "Video files";
          } else if (filterStr == "text/*") {
            extensions << "*.txt" << "*.csv" << "*.log" << "*.md";
            description = "Text files";
          } else {
            extensions << "*.*";
          }
        } else {
          if (!filterStr.startsWith("*")) {
            extensions << "*." + filterStr;
          } else {
            extensions << filterStr;
          }
        }
      }

      if (!extensions.isEmpty()) {
        QString qtFilter = QString("%1 (%2)").arg(description, extensions.join(" "));
        filters << qtFilter;

        filters << "All files (*.*)";
      }
    }

    if (filters.isEmpty()) {
      filters << "All files (*.*)";
    }
    pCefViewPrivate_->onFileDialog(mode,
                                   title.ToString().c_str(),
                                   default_file_path.ToString().c_str(),
                                   filters,
#if CEF_VERSION_MAJOR < 102
                                   selected_accept_filter,
#endif
                                   callback);
  });
  return true;
}
