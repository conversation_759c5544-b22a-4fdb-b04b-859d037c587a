<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: QCefEvent</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_q_cef_event.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_q_cef_event-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">QCefEvent</div></div>
</div><!--header-->
<div class="contents">

<p>Represents the event sent from native context(C/C++ code) to the web context(javascript)  
 <a href="#details">More...</a></p>

<p><code>#include &lt;QCefEvent.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab444dcc856db38dcc679db326ef22bf5" id="r_ab444dcc856db38dcc679db326ef22bf5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab444dcc856db38dcc679db326ef22bf5">QCefEvent</a> ()</td></tr>
<tr class="memdesc:ab444dcc856db38dcc679db326ef22bf5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs an event instance.  <br /></td></tr>
<tr class="separator:ab444dcc856db38dcc679db326ef22bf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b2b8bacbfebefe302cd1fab91cd5e8c" id="r_a2b2b8bacbfebefe302cd1fab91cd5e8c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2b2b8bacbfebefe302cd1fab91cd5e8c">QCefEvent</a> (const QString &amp;name)</td></tr>
<tr class="memdesc:a2b2b8bacbfebefe302cd1fab91cd5e8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs an event instance with name.  <br /></td></tr>
<tr class="separator:a2b2b8bacbfebefe302cd1fab91cd5e8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a357d5cb242977682523e69d501c673d4" id="r_a357d5cb242977682523e69d501c673d4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a357d5cb242977682523e69d501c673d4">QCefEvent</a> (const <a class="el" href="class_q_cef_event.html">QCefEvent</a> &amp;other)</td></tr>
<tr class="memdesc:a357d5cb242977682523e69d501c673d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs an event instance from existing one.  <br /></td></tr>
<tr class="separator:a357d5cb242977682523e69d501c673d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95e2f8c582270de0f9501945a6e063ee" id="r_a95e2f8c582270de0f9501945a6e063ee"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_q_cef_event.html">QCefEvent</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a95e2f8c582270de0f9501945a6e063ee">operator=</a> (const <a class="el" href="class_q_cef_event.html">QCefEvent</a> &amp;other)</td></tr>
<tr class="memdesc:a95e2f8c582270de0f9501945a6e063ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Assigns an existing event instance to current.  <br /></td></tr>
<tr class="separator:a95e2f8c582270de0f9501945a6e063ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c0e38242fa1ba823f1664232966787c" id="r_a5c0e38242fa1ba823f1664232966787c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5c0e38242fa1ba823f1664232966787c">~QCefEvent</a> ()</td></tr>
<tr class="memdesc:a5c0e38242fa1ba823f1664232966787c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destructs the event instance.  <br /></td></tr>
<tr class="separator:a5c0e38242fa1ba823f1664232966787c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5db1273fdda416900f5b7b26a119c85a" id="r_a5db1273fdda416900f5b7b26a119c85a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5db1273fdda416900f5b7b26a119c85a">setEventName</a> (const QString &amp;name)</td></tr>
<tr class="memdesc:a5db1273fdda416900f5b7b26a119c85a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the event name.  <br /></td></tr>
<tr class="separator:a5db1273fdda416900f5b7b26a119c85a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a970c76a348788b15a040c8c405a103" id="r_a5a970c76a348788b15a040c8c405a103"><td class="memItemLeft" align="right" valign="top">const QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5a970c76a348788b15a040c8c405a103">eventName</a> () const</td></tr>
<tr class="memdesc:a5a970c76a348788b15a040c8c405a103"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the event name.  <br /></td></tr>
<tr class="separator:a5a970c76a348788b15a040c8c405a103"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac84ba1292bcf56abdc5c6c4245aa6c04" id="r_ac84ba1292bcf56abdc5c6c4245aa6c04"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac84ba1292bcf56abdc5c6c4245aa6c04">setArguments</a> (const QVariantList &amp;args)</td></tr>
<tr class="memdesc:ac84ba1292bcf56abdc5c6c4245aa6c04"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the argument list.  <br /></td></tr>
<tr class="separator:ac84ba1292bcf56abdc5c6c4245aa6c04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cf70fa60235d723b9e578cded919327" id="r_a4cf70fa60235d723b9e578cded919327"><td class="memItemLeft" align="right" valign="top">QVariantList &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4cf70fa60235d723b9e578cded919327">arguments</a> ()</td></tr>
<tr class="memdesc:a4cf70fa60235d723b9e578cded919327"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the argument list.  <br /></td></tr>
<tr class="separator:a4cf70fa60235d723b9e578cded919327"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Represents the event sent from native context(C/C++ code) to the web context(javascript) </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="ab444dcc856db38dcc679db326ef22bf5" name="ab444dcc856db38dcc679db326ef22bf5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab444dcc856db38dcc679db326ef22bf5">&#9670;&#160;</a></span>QCefEvent() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QCefEvent::QCefEvent </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructs an event instance. </p>

</div>
</div>
<a id="a2b2b8bacbfebefe302cd1fab91cd5e8c" name="a2b2b8bacbfebefe302cd1fab91cd5e8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2b2b8bacbfebefe302cd1fab91cd5e8c">&#9670;&#160;</a></span>QCefEvent() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QCefEvent::QCefEvent </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>name</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructs an event instance with name. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>The event name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a357d5cb242977682523e69d501c673d4" name="a357d5cb242977682523e69d501c673d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a357d5cb242977682523e69d501c673d4">&#9670;&#160;</a></span>QCefEvent() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QCefEvent::QCefEvent </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="class_q_cef_event.html">QCefEvent</a> &amp;</td>          <td class="paramname"><span class="paramname"><em>other</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructs an event instance from existing one. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">other</td><td>The other event instance</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5c0e38242fa1ba823f1664232966787c" name="a5c0e38242fa1ba823f1664232966787c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c0e38242fa1ba823f1664232966787c">&#9670;&#160;</a></span>~QCefEvent()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QCefEvent::~QCefEvent </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Destructs the event instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a4cf70fa60235d723b9e578cded919327" name="a4cf70fa60235d723b9e578cded919327"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4cf70fa60235d723b9e578cded919327">&#9670;&#160;</a></span>arguments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QVariantList &amp; QCefEvent::arguments </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets the argument list. </p>
<dl class="section return"><dt>Returns</dt><dd>The argument list</dd></dl>

</div>
</div>
<a id="a5a970c76a348788b15a040c8c405a103" name="a5a970c76a348788b15a040c8c405a103"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5a970c76a348788b15a040c8c405a103">&#9670;&#160;</a></span>eventName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const QString QCefEvent::eventName </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets the event name. </p>
<dl class="section return"><dt>Returns</dt><dd>The event name</dd></dl>

</div>
</div>
<a id="a95e2f8c582270de0f9501945a6e063ee" name="a95e2f8c582270de0f9501945a6e063ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95e2f8c582270de0f9501945a6e063ee">&#9670;&#160;</a></span>operator=()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_q_cef_event.html">QCefEvent</a> &amp; QCefEvent::operator= </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="class_q_cef_event.html">QCefEvent</a> &amp;</td>          <td class="paramname"><span class="paramname"><em>other</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Assigns an existing event instance to current. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">other</td><td>The other event instance</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac84ba1292bcf56abdc5c6c4245aa6c04" name="ac84ba1292bcf56abdc5c6c4245aa6c04"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac84ba1292bcf56abdc5c6c4245aa6c04">&#9670;&#160;</a></span>setArguments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QCefEvent::setArguments </td>
          <td>(</td>
          <td class="paramtype">const QVariantList &amp;</td>          <td class="paramname"><span class="paramname"><em>args</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Sets the argument list. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">args</td><td>The argument list</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5db1273fdda416900f5b7b26a119c85a" name="a5db1273fdda416900f5b7b26a119c85a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5db1273fdda416900f5b7b26a119c85a">&#9670;&#160;</a></span>setEventName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QCefEvent::setEventName </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;</td>          <td class="paramname"><span class="paramname"><em>name</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Sets the event name. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>The name to be set</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_q_cef_event.html">QCefEvent</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
