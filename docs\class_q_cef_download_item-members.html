<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Member List</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_q_cef_download_item.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">QCefDownloadItem Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a723b2081749a447049efd04e768d9e57">cancel</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#af97dad25e439c8b997d6689fe1c91bf8">contentDisposition</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a5c9648906b02ce59aa6d82f03e468c1d">currentSpeed</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a61b2bd3cdc2f35d2424ded4e8b3801fd">endTime</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a40227300f52cd34a67281eacf95bcc28">fullPath</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#aff911ad0aa21867cdfe56457293f4914">id</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#ada41b0480a3e97d788086ea8c420a22c">isCanceled</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a67c86edf9e9cb742050e2521b55c86df">isComplete</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a22455702c989d4dcc08fb1d13659739d">isInProgress</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a6febee1fe8a5f7d15e888a4352d50526">isStarted</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#afb8ec36477b64a222b0610c8518f8e31">mimeType</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a9f9bcb65952b50b4f24c4d55eff9b64e">originalUrl</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#aaa31f59599e9311f0aee99a0a13e2568">pause</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a18fafe4f375aa60e43ec7c5d855ab91e">percentComplete</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a5d76f04bb41c5e4edad6d680b01fe63f">receivedBytes</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#aa2afc687e69c7c78f6c49e9a41a34823">resume</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a315592aa53a2bf7bc8aea717195f5b43">start</a>(const QString &amp;path, bool useDefaultDialog=true) const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#afe0d048a282cb605da910de1c5d82242">startTime</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#ac0dd9f8bea7ca594f04935d81cfb72a4">suggestedFileName</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#ab1ff7414475451351e4d4ab50f723bee">totalBytes</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a253cf33b582e3994b65d5611ef277bbe">url</a>() const</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_download_item.html#a913a2654dcd05ef8facefc515d831124">~QCefDownloadItem</a>()</td><td class="entry"><a class="el" href="class_q_cef_download_item.html">QCefDownloadItem</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
