<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Introduction</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('index.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Introduction </div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul>
  <li class="level1">
    <a href="#autotoc_md1">What is QCefView?</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md2">Why Choose QCefView over Electron?</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md3">Ideal Use Cases for QCefView</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md4">Alternatives to Qt</a>
    <ul>
      <li class="level2">
        <a href="#autotoc_md5">Unreal Game Development</a>
      </li>
      <li class="level2">
        <a href="#autotoc_md6">macOS App Development</a>
      </li>
    </ul>
  </li>
</ul>
</div>
<div class="textblock"><p><a class="anchor" id="md_docs_200-_introduction"></a></p>
<h1><a class="anchor" id="autotoc_md1"></a>
What is QCefView?</h1>
<p><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> is a powerful Qt Widget that seamlessly integrates the <a href="https://github.com/chromiumembedded/cef">Chromium Embedded Framework</a>. It empowers you to build applications leveraging CEF's robust capabilities within the familiar Qt ecosystem.</p>
<p>With <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>, you can:</p>
<ul>
<li>Develop applications using familiar Qt forms, signals, and slots.</li>
<li>Achieve straightforward interoperability between Web (JavaScript) and Native (C++) components.</li>
</ul>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadCenter">Windows   </th><th class="markdownTableHeadCenter">macOS   </th><th class="markdownTableHeadLeft">Linux    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyCenter"><p class="starttd"><img src="windows-demo.png" alt="" class="inline" title="Windows"/>    </p>
<p class="endtd"></p>
</td><td class="markdownTableBodyCenter"><p class="starttd"><img src="macOS-demo.png" alt="" class="inline" title="macOS"/>    </p>
<p class="endtd"></p>
</td><td class="markdownTableBodyLeft"><p class="starttd"><img src="linux-demo.png" alt="" class="inline" title="Linux"/>    </p>
<p class="endtd"></p>
</td></tr>
</table>
<h1><a class="anchor" id="autotoc_md2"></a>
Why Choose QCefView over Electron?</h1>
<p><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> and Electron serve different purposes and cater to distinct development styles. Here's a comparison:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Feature   </th><th class="markdownTableHeadNone"><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>   </th><th class="markdownTableHeadNone">Electron    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">Scope   </td><td class="markdownTableBodyNone">Qt UI Component   </td><td class="markdownTableBodyNone">Comprehensive Application Framework    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">Target Audience   </td><td class="markdownTableBodyNone">Native (C++) developers   </td><td class="markdownTableBodyNone">Frontend developers    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">Primary Language   </td><td class="markdownTableBodyNone">C++   </td><td class="markdownTableBodyNone">JavaScript    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">Interoperability   </td><td class="markdownTableBodyNone">Direct, straightforward Web/Native communication   </td><td class="markdownTableBodyNone">Requires plugins for Native integration    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">Use Case   </td><td class="markdownTableBodyNone">Embedding web UI within a native application   </td><td class="markdownTableBodyNone">Building cross-platform desktop applications primarily with web technologies   </td></tr>
</table>
<p>In essence:</p>
<ul>
<li><b><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a></b> is a component within the Qt framework, ideal for enhancing native applications with web-based UI elements.</li>
<li><b>Electron</b> is a complete framework for building cross-platform desktop applications using web technologies.</li>
</ul>
<h1><a class="anchor" id="autotoc_md3"></a>
Ideal Use Cases for QCefView</h1>
<p><a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> excels in scenarios where you want to combine the power of web frontend technologies for the UI with the performance and control of native languages for core functionality.</p>
<p>Consider <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> for applications like:</p>
<ul>
<li><b>Multimedia Players:</b> Leverage web technologies for rich, dynamic user interfaces.</li>
<li><b>Game Platforms/Launchers:</b> Create visually appealing and interactive frontends for native game engines.</li>
<li><b>Utility Applications:</b> Build feature-rich tools with complex UIs that benefit from web-based rendering.</li>
<li><b>Custom Embedded Browsers (with limitations):</b> Embed web content with a high degree of control over the rendering process.</li>
</ul>
<p>In these context-driven applications, web frontend technologies are excellent for displaying lists, tables, and complex pages with engaging effects. <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> acts as a WebApp container, allowing you to host your web UI while retaining your hard-core business logic as native components. The interoperability provided by <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> seamlessly bridges the gap between the web and native worlds.</p>
<dl class="section note"><dt>Note</dt><dd>If your primary goal is to develop a full-fledged web browser application, <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> might not be the optimal choice. <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> is designed as a UI component and intentionally disables certain browser-specific features. For building a complete browser, consider using the original CEF library directly.</dd></dl>
<h1><a class="anchor" id="autotoc_md4"></a>
Alternatives to Qt</h1>
<h2><a class="anchor" id="autotoc_md5"></a>
Unreal Game Development</h2>
<p>For Unreal Engine based game developers, you may want to try <a href="https://cefview.github.io/UCefView">UCefView</a> plugin. This is a powerful stable and flexible WebView component designed for Unreal Engine.</p>
<h2><a class="anchor" id="autotoc_md6"></a>
macOS App Development</h2>
<p>If you prefer not to use Qt, you can still leverage CEF by integrating it with other frameworks. For example, you can integrate CEF with the Cocoa framework, as demonstrated in the <a href="https://github.com/CefView/CocoaCefView">CocoaCefView</a> project. This allows you to create custom CEF views tailored to your specific needs. </p>
</div></div><!-- PageDoc -->
<a href="doxygen_crawl.html"></a>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
