<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Member List</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_q_cef_view.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">QCefView Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_q_cef_view.html">QCefView</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a503148f8ff5ca5b28d3f0e123bf5bf76">addArchiveResource</a>(const QString &amp;path, const QString &amp;url, const QString &amp;password=&quot;&quot;, int priority=0)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a8c6286b279094a691832fc89b93c75f1">addLocalFolderResource</a>(const QString &amp;path, const QString &amp;url, int priority=0)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a93bf80d520a6d1da560bb1729d4b5152">addressChanged</a>(const QString &amp;frameId, const QString &amp;url)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#aa0b341726ea511a8a4c0bf6b603da5f7">AllFrameID</a></td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ad5455e3a8cb0ffa1f9d7cb98307a6bb4">broadcastEvent</a>(const QCefEvent &amp;event)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a01100f7ab97f9f643e4f23af5cea9900">browserCanGoBack</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ae1d6a6d94e02a54654463e5b0c491624">browserCanGoForward</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#aa82e1ceaa7f543f8d99d7d9cbf714382">browserGoBack</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9ce96fbf25701594b8d65381661141db">browserGoForward</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ae4aa1652bf9852ed744dd38487bbb748">browserId</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a12ca0bc0f4fc55dfc838769990d6a6d7">browserIsLoading</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a45bc8cd7340ce410cf873d7296ffacf6">browserReload</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#afadd210f3c5cd95598b18aa158a9c16f">browserStopLoad</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#aef058b415485dba45c8dfffdcf956a5f">cefQueryRequest</a>(const int &amp;browserId, const QString &amp;frameId, const QCefQuery &amp;query)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#add5abd934b15c1b8b3e91703701a8cf4">cefUrlRequest</a>(const int &amp;browserId, const QString &amp;frameId, const QString &amp;url)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787">CefWindowOpenDisposition</a> enum name</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a48d5eb6b5d1564c6668f15f1f2a45b24">CefWindowOpenDispositionCurrentTab</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a66fc224542ebf19b10e51aa721b1ef09">CefWindowOpenDispositionIgnoreAction</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787ab43a4c561c1e83ac6419cc711220e62c">CefWindowOpenDispositionNewBackgroundTab</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a8bd674f0da6b33d87117c7b8bf5153c2">CefWindowOpenDispositionNewForeGroundTab</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a844b09433f6bcfe05e43fcd8c848a41d">CefWindowOpenDispositionNewPopup</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a7b2dddad8191aff9c281badd83b710b1">CefWindowOpenDispositionNewWindow</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a5e8deb29da2ecf34eaaf23f1ed7201f7">CefWindowOpenDispositionOffTheRecord</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a664b036a79acb7f6c84c2cfdedbd4093">CefWindowOpenDispositionSaveToDisk</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787a3d46df185e4a92deef33f80cfdae4c5c">CefWindowOpenDispositionSingletonTab</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9963d810f8aa71b45b1b10f0abbe8787af5284fd406b0fe65ef1071961543d3a9">CefWindowOpenDispositionUnknown</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#abdf0a68139fe9163ecd9b5a0cdeed6d7">closeDevTools</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a2dae6946082712815273c2967d37762a">consoleMessage</a>(const QString &amp;message, int level)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ac8a83d1f2fb0e771fb48007838b40d1f">contextMenuEvent</a>(QContextMenuEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ae11274817f627abf9d407e12dcd5c050">draggableRegionChanged</a>(const QRegion &amp;draggableRegion, const QRegion &amp;nonDraggableRegion)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#aef5bf034432e297e89cfd45aca68f5ff">executeJavascript</a>(const QString &amp;frameId, const QString &amp;code, const QString &amp;url)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ac13fdea19da380026a742a60c51a2356">executeJavascriptWithResult</a>(const QString &amp;frameId, const QString &amp;code, const QString &amp;url, const QString &amp;context)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a7b9e104469c1a4a203c2e1d7e9cfd2a7">faviconURLChanged</a>(const QStringList &amp;urls)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a05bf10e1e318cf9cc4ad742ad61c9706">focusInEvent</a>(QFocusEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a414b4c9efe5edd10c324c1e35e12d07c">focusOutEvent</a>(QFocusEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#afa0fbb5b4c2c6c3592085e9b94dffc4a">fullscreenModeChanged</a>(bool fullscreen)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a85fd904cbd3b91a72ce090cffb0119c8">hasDevTools</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#af2432e14ac8d9156594c3941ff6b4d14">hideEvent</a>(QHideEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a02d713f4a0545e85832b70ddced7e831">inputMethodEvent</a>(QInputMethodEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#af25a011c126a9bb5dc3df99756a75368">inputMethodQuery</a>(Qt::InputMethodQuery query) const override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a00435b9ab61d04517427dbe4805e970d">invokeMethod</a>(const int &amp;browserId, const QString &amp;frameId, const QString &amp;method, const QVariantList &amp;arguments)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a2a8a2ebaedb88ccd80536c66d878ff8a">isDragAndDropEnabled</a>() const</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#abb79735affb74166c0bed7f361ce1388">isPopupContextMenuDisabled</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a6aa89a0ce04dac5aa2c01545253ffc56">keyPressEvent</a>(QKeyEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ab2d8e99188937bba13893ce6c54f9a3f">keyReleaseEvent</a>(QKeyEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a346fde9e6ed939e82aad237fbb39cb6f">leaveEvent</a>(QEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a567e20fd09518ca9c0d2e82f936ff5d6">loadEnd</a>(const int &amp;browserId, const QString &amp;frameId, bool isMainFrame, int httpStatusCode)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a460c00b974b6368ad8d1a5975d3aaf7d">loadError</a>(const int &amp;browserId, const QString &amp;frameId, bool isMainFrame, int errorCode, const QString &amp;errorMsg, const QString &amp;failedUrl)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ac7377eeed6811bafca1e6887b64d62a5">loadingProgressChanged</a>(double progress)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a076678fb5d8deec1600f369d4f1fc95a">loadingStateChanged</a>(const int &amp;browserId, bool isLoading, bool canGoBack, bool canGoForward)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a6b04776ad4d7b62c102e9c38ac022b40">loadStart</a>(const int &amp;browserId, const QString &amp;frameId, bool isMainFrame, int transitionType)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#aecdaec6088be4f77a505e0fae0765625">MainFrameID</a></td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a3bd541e981d7dbad0deceb64df0d3a5b">mouseMoveEvent</a>(QMouseEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#aac476c39493a4e75e681b9e09f13e060">mousePressEvent</a>(QMouseEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a69306a82128ba3e525103eb132aae62c">mouseReleaseEvent</a>(QMouseEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a543b0eca797c5161d6325665d5ddd576">nativeBrowserCreated</a>(QWindow *window)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ac3b8ec3b088422a67f93fc580047a1a0">navigateToString</a>(const QString &amp;content)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a4bf036d5a7d128d4c1487afaa0393d5b">navigateToUrl</a>(const QString &amp;url)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a49c99555c9c604dc2e310c3df02bf385">onNewBrowser</a>(const QString &amp;sourceFrameId, const QString &amp;url, const QString &amp;name, QCefView::CefWindowOpenDisposition targetDisposition, QRect &amp;rect, QCefSetting &amp;settings)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a666e93d6e9f9be7444f9b898f77c8292">onNewDownloadItem</a>(const QSharedPointer&lt; QCefDownloadItem &gt; &amp;item, const QString &amp;suggestedName)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#aadb6d47674e2ad414eb20a066b7e0738">onNewPopup</a>(const QString &amp;frameId, const QString &amp;targetUrl, QString &amp;targetFrameName, QCefView::CefWindowOpenDisposition targetDisposition, QRect &amp;rect, QCefSetting &amp;settings, bool &amp;disableJavascriptAccess)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ad23057b6187876c84f883b1e7120456d">onRequestCloseFromWeb</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9833a1db8ef5864489f9462ef397cbb8">onUpdateDownloadItem</a>(const QSharedPointer&lt; QCefDownloadItem &gt; &amp;item)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ad88e5a99ad808b7a911b58ba9ed9b838">paintEngine</a>() const override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#aa205502bb5238e6e2ce727046ed8a9b8">paintEvent</a>(QPaintEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a203cdf24f64a5582f7c79e2401e9d8ca">QCefView</a>(const QString &amp;url, const QCefSetting *setting, QWidget *parent=nullptr, Qt::WindowFlags f=Qt::WindowFlags())</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a84f33f3697e39588e9b76d2cd4847892">QCefView</a>(QWidget *parent=nullptr, Qt::WindowFlags f=Qt::WindowFlags())</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ad098ad3ed18da915880f21df52817fa9">render</a>(QPainter *painter)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a9b69938aa6f8352799bee6084bab03e4">reportJavascriptResult</a>(const int &amp;browserId, const QString &amp;frameId, const QString &amp;context, const QVariant &amp;result)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a237797e9f77342d72c35a8017865988e">resizeEvent</a>(QResizeEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#afc838dab54d2b083b05f0d98349b50cc">responseQCefQuery</a>(const QCefQuery &amp;query)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#acca71443b26dce09e81e3f937cedaa6b">setDisablePopupContextMenu</a>(bool disable)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#af73ef1d6f77a31b528c729cf7379abfb">setEnableDragAndDrop</a>(bool enable)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a9b1b42857e38a9f5c6c810fd51593788">setFocus</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a61ad737cd2354021f8310f323f4f8ada">setFocus</a>(Qt::FocusReason reason)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a2b4b3da8874855bbe2d558081233d948">setPreference</a>(const QString &amp;name, const QVariant &amp;value, const QString &amp;error)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a61845e6e370a57be5f3662ba37cd7b29">showDevTools</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a08dcba31e0d2860270ab3cd8055a5c4e">showEvent</a>(QShowEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a86d10c28b8821a36723e3504fa0cc7e7">statusMessage</a>(const QString &amp;message)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a48c82c208cab769a1baa7177bc58b030">titleChanged</a>(const QString &amp;title)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#ac47c23ffcd94bdffe2b6a81eaae077a2">triggerEvent</a>(const QCefEvent &amp;event)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#ab70a707afda924d6f035b20a1aecc695">triggerEvent</a>(const QCefEvent &amp;event, const QString &amp;frameId)</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_view.html#a3395f62959288420a834c736933e7228">wheelEvent</a>(QWheelEvent *event) override</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_view.html#a70903dca8ccd3e2776d68742531177fd">~QCefView</a>()</td><td class="entry"><a class="el" href="class_q_cef_view.html">QCefView</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
