<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Rendering</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('md_docs_205-_rendering.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Rendering</div></div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul>
  <li class="level1">
    <a href="#autotoc_md50">NCW mode</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md51">OSR mode</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md52">Setting OSR Mode in QCefView</a>
  </li>
  <li class="level1">
    <a href="#autotoc_md53">Hardware Acceleration</a>
  </li>
</ul>
</div>
<div class="textblock"><p><a class="anchor" id="autotoc_md49"></a></p>
<p>CEF supports native child window mode and OSR mode, in <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> both modes are implemented.</p>
<h1><a class="anchor" id="autotoc_md50"></a>
NCW mode</h1>
<p>When using NCW mode, CEF handles the creation and management of a separate native window for the browser. <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> then obtains a handle to this window and embeds it within a Qt widget. This allows the browser content to be displayed as part of the Qt application's user interface.</p>
<ul>
<li><b>Advantages:</b><ul>
<li>Simpler to implement compared to OSR mode.</li>
<li>Generally better performance for basic web content rendering, as the rendering is handled directly by CEF within its own window.</li>
<li>Leverages the native windowing system for event handling and input management.</li>
</ul>
</li>
<li><b>Disadvantages:</b><ul>
<li>Less flexibility in terms of customizing the rendering process.</li>
<li>Potential limitations in scenarios requiring advanced integration with other graphics APIs or custom rendering pipelines.</li>
<li>Can be problematic when trying to overlay Qt widgets on top of the browser window due to the z-ordering of native windows.</li>
</ul>
</li>
<li><b>Integration with Qt:</b> <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> uses platform-specific APIs to obtain the native window handle from CEF and embed it within a QWidget. This allows the browser content to be seamlessly integrated into the Qt application's layout and user interface.</li>
</ul>
<h1><a class="anchor" id="autotoc_md51"></a>
OSR mode</h1>
<p>When using OSR mode, CEF renders the web page content into a buffer (either in CPU memory or as a shared texture in GPU memory). <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> then takes this buffer and displays it within a Qt widget. This gives you more control over how the web content is rendered and integrated into your application.</p>
<ul>
<li><b>Advantages:</b><ul>
<li>Greater flexibility in customizing the rendering process.</li>
<li>Enables advanced features such as custom rendering, compositing, and integration with other graphics APIs.</li>
<li>Allows for more seamless integration of web content with Qt widgets, as you can directly manipulate the rendered output.</li>
</ul>
</li>
<li><b>Disadvantages:</b><ul>
<li>More complex to implement compared to NCW mode.</li>
<li>Can be more CPU-intensive, especially if hardware acceleration is not enabled or not functioning correctly.</li>
<li>Requires careful management of memory and rendering resources.</li>
</ul>
</li>
<li><b>Integration with Qt:</b> <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> uses Qt's rendering APIs (such as QPainter or QOpenGLWidget) to display the buffer provided by CEF. This allows you to integrate the web content seamlessly into your Qt application's user interface and apply custom transformations, effects, and overlays.</li>
</ul>
<h1><a class="anchor" id="autotoc_md52"></a>
Setting OSR Mode in QCefView</h1>
<p>To enable OSR mode in <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>, you need to configure it using <code><a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a></code></p>
<div class="fragment"><div class="line"><a class="code hl_class" href="class_q_cef_config.html">QCefConfig</a> config;</div>
<div class="line">config.<a class="code hl_function" href="class_q_cef_config.html#af6041bcae9fcf72ea47ffc47d62e5a96">setWindowlessRenderingEnabled</a>(<span class="keyword">true</span>); <span class="comment">// Enable OSR mode</span></div>
<div class="ttc" id="aclass_q_cef_config_html"><div class="ttname"><a href="class_q_cef_config.html">QCefConfig</a></div><div class="ttdoc">Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...</div><div class="ttdef"><b>Definition</b> QCefConfig.h:28</div></div>
<div class="ttc" id="aclass_q_cef_config_html_af6041bcae9fcf72ea47ffc47d62e5a96"><div class="ttname"><a href="class_q_cef_config.html#af6041bcae9fcf72ea47ffc47d62e5a96">QCefConfig::setWindowlessRenderingEnabled</a></div><div class="ttdeci">void setWindowlessRenderingEnabled(const bool enabled)</div><div class="ttdoc">Sets the flag to enable/disable OSR mode.</div></div>
</div><!-- fragment --><p>By setting <code>config.setWindowlessRenderingEnabled(true)</code>, you instruct <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> to use OSR (Off-Screen Rendering) mode. This configuration must be set before the <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> instance is created.</p>
<dl class="section note"><dt>Note</dt><dd><a class="el" href="class_q_cef_config.html" title="Represents the CEF setting. For more details please refer to: https://bitbucket.org/chromiumembedded/...">QCefConfig</a> works in the application scope, so you can only choose to use NCW or OSR exclusively for all <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> instances. This means that once you set <code><a class="el" href="class_q_cef_config.html#af6041bcae9fcf72ea47ffc47d62e5a96" title="Sets the flag to enable/disable OSR mode.">QCefConfig::setWindowlessRenderingEnabled</a></code> to <code>true</code> or <code>false</code>, all <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> instances in your application will use the same rendering mode.</dd></dl>
<h1><a class="anchor" id="autotoc_md53"></a>
Hardware Acceleration</h1>
<p>With OSR mode enabled, you can choose to use hardware acceleration.</p>
<p>When hardware acceleration is enabled, CEF renders the browser content to a shared texture in the GPU. <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> then uses device-dependent technologies (DirectX, Metal, or OpenGL) to render the browser content to the user. This typically results in significantly better performance, especially for complex web content and animations.</p>
<p>When hardware acceleration is disabled, CEF provides a CPU memory buffer containing image color data to <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a>. <a class="el" href="class_q_cef_view.html" title="Represents the CEF browser view.">QCefView</a> then renders this buffer using Qt's software rasterizer. This mode can be useful in environments where hardware acceleration is not available or is causing issues.</p>
<p>To control hardware acceleration, you can use the following settings in <code><a class="el" href="class_q_cef_setting.html" title="Represents the settings for individual browser.">QCefSetting</a></code>:</p>
<div class="fragment"><div class="line"><a class="code hl_class" href="class_q_cef_setting.html">QCefSetting</a> setting;</div>
<div class="line">setting.<a class="code hl_function" href="class_q_cef_setting.html#a6dbd7b1da3a151294e8bf020a16687be">setBackgroundColor</a>(Qt::gray);</div>
<div class="line">setting.<a class="code hl_function" href="class_q_cef_setting.html#a4a5810da8e070288ff80c069f5b52f23">setWindowlessFrameRate</a>(1000);</div>
<div class="line"> </div>
<div class="line"><span class="comment">// enable hardware acceleration</span></div>
<div class="line">setting.<a class="code hl_function" href="class_q_cef_setting.html#afdb320899b859e7781458a281a9dafbe">setHardwareAcceleration</a>(<span class="keyword">true</span>);</div>
<div class="ttc" id="aclass_q_cef_setting_html"><div class="ttname"><a href="class_q_cef_setting.html">QCefSetting</a></div><div class="ttdoc">Represents the settings for individual browser.</div><div class="ttdef"><b>Definition</b> QCefSetting.h:27</div></div>
<div class="ttc" id="aclass_q_cef_setting_html_a4a5810da8e070288ff80c069f5b52f23"><div class="ttname"><a href="class_q_cef_setting.html#a4a5810da8e070288ff80c069f5b52f23">QCefSetting::setWindowlessFrameRate</a></div><div class="ttdeci">void setWindowlessFrameRate(const int value)</div><div class="ttdoc">Sets the frame rate in window less mode.</div></div>
<div class="ttc" id="aclass_q_cef_setting_html_a6dbd7b1da3a151294e8bf020a16687be"><div class="ttname"><a href="class_q_cef_setting.html#a6dbd7b1da3a151294e8bf020a16687be">QCefSetting::setBackgroundColor</a></div><div class="ttdeci">void setBackgroundColor(const QColor &amp;value)</div><div class="ttdoc">Sets the background color.</div></div>
<div class="ttc" id="aclass_q_cef_setting_html_afdb320899b859e7781458a281a9dafbe"><div class="ttname"><a href="class_q_cef_setting.html#afdb320899b859e7781458a281a9dafbe">QCefSetting::setHardwareAcceleration</a></div><div class="ttdeci">void setHardwareAcceleration(const bool value)</div></div>
</div><!-- fragment --><p>By default, CEF attempts to use hardware acceleration if it is available. However, explicitly setting the <code>setHardwareAcceleration</code> provides more control.</p>
<dl class="section remark"><dt>Remarks</dt><dd>Enabling hardware acceleration is generally recommended for the best performance. However, if you encounter rendering issues or compatibility problems, disabling it can provide a fallback option. And this feature only works in OSR mode. </dd></dl>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
