<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Member List</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_q_cef_setting.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">QCefSetting Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_q_cef_setting.html">QCefSetting</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a9db276640c98e1d5d38290e06ae17d1a">backgroundColor</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a86526505941342ffbc96b6e5226ffcbe">cursiveFontFamily</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a94b2a957c8754619df7a37258a41c31d">databases</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a2de89967072377f279b36e821bde18e1">defaultEncoding</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#ab25a8bd4b9d5acb865c840b8c4320f5d">defaultFixedFontSize</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a4a38db62a20b2e8e7a26029f0b928689">defaultFontSize</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a0acd7303ff77ae1eff9fa579fc96e97a">fantasyFontFamily</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a239e1982f4ab4198a62f9c00ae17bcbb">fixedFontFamily</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a7509581e3295f2585394329a1adec734">hardwareAcceleration</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#aa06779c82a28a01bd862ac605356be40">imageLoading</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a7374edce9be761b5d40edef80f42f7b9">imageShrinkStandaloneToFit</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#abfa7cceca149b6311e7a28e6ae4e2853">javascript</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#aff511401ea2d0453583db4b642526dbd">javascriptAccessClipboard</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a8cab27275ce36cb0b1477a85b6694f3b">javascriptCloseWindows</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a6696cc8fa0b4ea1ef14d71259cd4350d">javascriptDomPaste</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a4fffdf46c7617f7b116e2e836dbb48d0">localStorage</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a3b263c84f28ef76fc1dc4cd4dfad8e5d">minimumFontSize</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a3f0801d8ecbc7fe86df805dc6db3aad9">minimumLogicalFontSize</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a486f0c9edda93fb30c75608ac9b98ba2">operator=</a>(const QCefSetting &amp;other)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#afb8450a162ed9ce3f59a37491147db8d">QCefSetting</a>()</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#af650fcab674f8c33a996a2d8cd34eaef">QCefSetting</a>(const QCefSetting &amp;other)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a6b503e82defe4c57a88936fb2cec2509">remoteFonts</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a861fccc511473f01f74a5199d2660126">sansSerifFontFamily</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a43ab6247cbbaa59652846eb84760c1fb">serifFontFamily</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a6dbd7b1da3a151294e8bf020a16687be">setBackgroundColor</a>(const QColor &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#aef4eb96f03003eb774924fe418a7edf1">setCursiveFontFamily</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#ad0680b646641dd568b7de8ae072670db">setDatabases</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a599a011dff8d11c8201036238016a77f">setDefaultEncoding</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a4dddf809104f676469fc03b266d7a2ff">setDefaultFixedFontSize</a>(const int value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a85b9cadc6df83a3addbaf567df791339">setDefaultFontSize</a>(const int value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a831bed0440aed06894a85ee8dde74a05">setFantasyFontFamily</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#af11ccd0529a79efee12a3e728d24e641">setFixedFontFamily</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#afdb320899b859e7781458a281a9dafbe">setHardwareAcceleration</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a15457b991b298a722cbc9f9507d109fb">setImageLoading</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a562e4477613234a906b2d167473b0627">setImageShrinkStandaloneToFit</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a884abf03a17dc3fa4343b578445219c0">setJavascript</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#aff0a7e72f55a002f5731f4e202e45d63">setJavascriptAccessClipboard</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a7c3755e100310ab63a98cbd6b7c89a6b">setJavascriptCloseWindows</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a61c6207fc6fb05b71248fee8766d21a2">setJavascriptDomPaste</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#afca6695cdffbb1734588c33ffff3aa3c">setLocalStorage</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a0c1733e2e173cb462f0ec21a613b628e">setMinimumFontSize</a>(const int value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#aa6e22cc3cfa68ad13809b6766e9cafab">setMinimumLogicalFontSize</a>(const int value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#aba88a474960049cda01c7295e514eb8a">setRemoteFonts</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#aa07d27c4a22dc2ec0d041c9deda1d71b">setSansSerifFontFamily</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a9eadb4d8d6567c78d80f09e1ace1dd30">setSerifFontFamily</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#ad021537af966fb8f17d8a07066a5408e">setStandardFontFamily</a>(const QString &amp;value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a8ccb815304eeadba9d679186472d4e40">setTabToLinks</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a159239d8d7d5b4f944db0c9f37b10509">setTextAreaResize</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a984a074332bd07b78da2079d1d333209">setWebGL</a>(const bool value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a629a1139dff88c3bb85a4c9a620d0682">setWindowInitialSize</a>(const QSize &amp;size)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a4a5810da8e070288ff80c069f5b52f23">setWindowlessFrameRate</a>(const int value)</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#aa7a0cfa4086251bdfc95c4ae72e52896">standardFontFamily</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#a9b81d1bcf47146dfc5eeeabeb583e627">tabToLinks</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a8d88588797f311f71c55073f95dafafe">textAreaResize</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#aca84c981db4fcc2cf6aa3ece7c44973c">webGL</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#ad3aabefb7879e392952a94464c983978">windowInitialSize</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_setting.html#adfc424688213e0e695b6b84681581135">windowlessFrameRate</a>() const</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_setting.html#a589b16fe883213d0e330503c0ccab218">~QCefSetting</a>()</td><td class="entry"><a class="el" href="class_q_cef_setting.html">QCefSetting</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
