<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=9" />
    <meta name="generator" content="Doxygen 1.13.2" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>QCefView: Member List</title>
    <link href="tabs.css" rel="stylesheet" type="text/css" />
    <link rel="icon" type="image/svg+xml" href="logo.drawio.svg" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="dynsections.js"></script>
    <script type="text/javascript" src="doxygen-awesome-darkmode-toggle.js"></script>
    <script type="text/javascript" src="doxygen-awesome-fragment-copy-button.js"></script>
    <script type="text/javascript" src="doxygen-awesome-paragraph-link.js"></script>
    <script type="text/javascript" src="doxygen-awesome-interactive-toc.js"></script>
    <script type="text/javascript" src="doxygen-awesome-tabs.js"></script>
    <script type="text/javascript" src="toggle-alternative-theme.js"></script>
    <script type="text/javascript">
        DoxygenAwesomeFragmentCopyButton.init()
        DoxygenAwesomeDarkModeToggle.init()
        DoxygenAwesomeParagraphLink.init()
        DoxygenAwesomeInteractiveToc.init()
        DoxygenAwesomeTabs.init()
    </script>
    <link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link href="doxygen-awesome.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only.css" rel="stylesheet" type="text/css"/>
<link href="doxygen-awesome-sidebar-only-darkmode-toggle.css" rel="stylesheet" type="text/css"/>
<link href="custom.css" rel="stylesheet" type="text/css"/>
<link href="custom-alternative.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <!-- https://tholman.com/github-corners/ -->
    <a href="https://github.com/CefView/QCefView" class="github-corner" title="View source on GitHub" target="_blank"
        rel="noopener noreferrer">
        <svg viewBox="0 0 250 250" width="40" height="40"
            style="position: absolute; top: 0; border: 0; right: 0; z-index: 99;" aria-hidden="true">
            <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
            <path
                d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2"
                fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
            <path
                d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z"
                fill="currentColor" class="octo-body"></path>
        </svg></a>
    <style>
        .github-corner:hover .octo-arm {
            animation: octocat-wave 560ms ease-in-out
        }
        @keyframes octocat-wave {
            0%,
            100% {
                transform: rotate(0)
            }
            20%,
            60% {
                transform: rotate(-25deg)
            }
            40%,
            80% {
                transform: rotate(10deg)
            }
        }
        @media (max-width:500px) {
            .github-corner:hover .octo-arm {
                animation: none
            }
            .github-corner .octo-arm {
                animation: octocat-wave 560ms ease-in-out
            }
        }
    </style>
    <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
        <div id="titlearea">
            <table cellspacing="0" cellpadding="0">
                <tbody>
                    <tr style="height: 56px;">
                        <td id="projectalign" style="padding-left: 0.5em;">
                            <div id="projectname">QCefView
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search',true);
  $(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_q_cef_config.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">QCefConfig Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_q_cef_config.html">QCefConfig</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a2828af9a2815ddeb1026e2f6a760d5e8">acceptLanguageList</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a2873f9e8e8997db4060348418df16632">addCommandLineSwitch</a>(const QString &amp;smitch)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a141daa8b02526d190e462cbcb38dbab5">addCommandLineSwitchWithValue</a>(const QString &amp;smitch, const QString &amp;v)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#aa04db9637f47424834bbcdf05a8b640b">backgroundColor</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#aab1ee01c7697e38b94b8edf961da4b35">bridgeObjectName</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a2b15417d6066479256fc514721cd0474">browserSubProcessPath</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a626d58894a334167dfc3fbe4fa055711">builtinSchemeName</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ab3a95ce139ce862abb4abb300c1cc1e3">cachePath</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a892715bebd8dc71e5acb0be17bfff43d">commandLinePassthroughDisabled</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ac1d5ca26f596c9f3e7697da04e549414">locale</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a3d43450cd3768ff1783596e48fcfe707">localesDirectoryPath</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7c">LogLevel</a> enum name</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a8728d026571a97449e13e8502c34e5e5">logLevel</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7cafb3f2009094b0d1ff363969eb01ee94b">LOGSEVERITY_DEBUG</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7ca01b281485ae3ba1c2c608e92b81f8d60">LOGSEVERITY_DEFAULT</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7ca7b5b19b0c251025706099ca43e4492a7">LOGSEVERITY_DISABLE</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7ca7480fe3cac801638c8bb04aefb6abb3d">LOGSEVERITY_ERROR</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7ca4ac597d5afa3a624c8df22a538e0b11b">LOGSEVERITY_FATAL</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7cae85556e5aa93d733b265d282d43ccdc2">LOGSEVERITY_INFO</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7cadbf6a1df0d32aa49fb6b9158983435d3">LOGSEVERITY_VERBOSE</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#ae437cd58b60d3902bba07e75a48d9a7ca9074dc5764211db3e4de761451aa8422">LOGSEVERITY_WARNING</a> enum value</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a2f78eccb1b7463db2c0b174aff5d0553">operator=</a>(const QCefConfig &amp;other)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#aa8b22bc6b4d9ef5c8aeccfc363ee1f9c">persistSessionCookies</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a4749b6aa16660a15d753f5248985e25f">persistUserPreferences</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a2a937276cdbf76f77d2bf70a766c6412">QCefConfig</a>()</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#af90f0b9e087d39a6bd059701ee450516">QCefConfig</a>(const QCefConfig &amp;other)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#aeaa7b37b83ee32a5ec50a1dec11d0c2e">remoteDebuggingPort</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a45c8bed47089201d40124041b7499164">resourceDirectoryPath</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a5bc459471e82e88326c17220a0e05310">rootCachePath</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a8abeb109ccc2c7971afb98efee06735e">sandboxDisabled</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a360c26dd512b9a4a3d6596c0590c370b">setAcceptLanguageList</a>(const QString &amp;languages)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a2ef252883876dd17193212c52bd02fc0">setBackgroundColor</a>(const QColor &amp;color)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a03687393e227bc8747bdc9ffa7400d60">setBridgeObjectName</a>(const QString &amp;name)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a3fca1b7b72f37f800278c743b74f1b82">setBrowserSubProcessPath</a>(const QString &amp;path)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a7e84d09e2bcacfc5fdfb8eeca49aca98">setBuiltinSchemeName</a>(const QString &amp;name)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#aa8f73284ec9ed73dc2028b8c89e8e3c8">setCachePath</a>(const QString &amp;path)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#aefbb555266937e769ed2102df41b0599">setCommandLinePassthroughDisabled</a>(const bool disabled)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#af67e837996a1dd84af0866f76588ba4e">setLocale</a>(const QString &amp;locale)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a4af04a575ecd6b632a794c42144d03d8">setLocalesDirectoryPath</a>(const QString &amp;path)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a230ee52b4d64e0ea6f7ba5a4e9ac5f5e">setLogLevel</a>(const LogLevel lvl)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a04c4f9aa52131df29c4eb6abd48cc2f0">setPersistSessionCookies</a>(bool enabled)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a6c5c7d498a6c003166071ac6e4e7e359">setPersistUserPreferences</a>(bool enabled)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#ac502d5e4b911c4e57d6fe4167be6d801">setRemoteDebuggingPort</a>(short port)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a0690fb1cb1a3cd87c44be340b6308f42">setResourceDirectoryPath</a>(const QString &amp;path)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a768b9bc0368ac7a82f6e74aec536aa8f">setRootCachePath</a>(const QString &amp;path)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a09f3800b8911bad084b9e4673f1839b0">setSandboxDisabled</a>(const bool disabled)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a60009aad390599eb5857182a32de7f23">setUserAgent</a>(const QString &amp;agent)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#af6041bcae9fcf72ea47ffc47d62e5a96">setWindowlessRenderingEnabled</a>(const bool enabled)</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#ad95b55d57719d9fc1a3dc5abb5695016">userAgent</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_q_cef_config.html#a17b6765b357b7a2d69a1f02b27a4eb92">windowlessRenderingEnabled</a>() const</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_q_cef_config.html#a67d06ef56affa82e943c7a5c73afee9a">~QCefConfig</a>()</td><td class="entry"><a class="el" href="class_q_cef_config.html">QCefConfig</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
