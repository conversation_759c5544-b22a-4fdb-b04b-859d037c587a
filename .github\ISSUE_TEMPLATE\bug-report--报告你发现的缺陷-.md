---
name: Bug Report [报告你发现的缺陷]
about: Create a bug report to help us improve 【请使用此模板提交BUG报告，没有使用模板的issue将被直接关闭】
title: "[BUG]: "
labels: bug, feature, triage
assignees: tishion

---

[//]: # (以下所有的以"[//]: #"开始的文字都是注释，不会被markdown渲染，所以无需删除)
[//]: # (All lines start "with [//]: #" below are comment lines and will not be rebdered by markdown, so there's no need to delete them)


[//]: # (注意：如果你发现的BUG是由于使用了与当前代码库中不同的Qt/CEF版本，该BUG将会被忽略)
[//]: # (Node: if you find some bugs caused by using different CEF/Qt version with the one in current repo, this report will be ignored)

**Describe the bug 【Bug描述】** 

[//]: # (在下面填写描述BUG发生的表现)
[//]: # (A clear and concise description of what the bug is)
...


**To Reproduce 【复现步骤】**

[//]: # (在下面填写复现该BUG的详细操作步骤)
[//]: # (Steps to reproduce the behavior)
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior 【正确的预期行为】**

[//]: # (在下面填写如果不存在该BUG时候预期的正确行为)
[//]: # (A clear and concise description of what you expected to happen)
...

**Screenshots 【截图】**

[//]: # (如果可以请在下面请提供BUG的截图信息)
[//]: # (If applicable, add screenshots to help explain your problem)
...

**Environment 【BUG产生的环境】**

[//]: # (请提供以下信息，OSR和非OSR模式为必须提供项目，无此项目issue不会被处理)
[//]: # (Please complete the following information, OSR/NCW is mandatory)
 - OSR/NCW Mode:
 - OS & Version: [e.g. Windows/macOS/Linux]
 - Qt Version:
 - CEF Version:
 

**Additional context 【更多额外信息】**

[//]: # (如有更多额外信息，请在下面添加)
[//]: # (Add any other context about the problem here)
...
