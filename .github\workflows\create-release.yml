name: Create Release

on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"

env:
  # Customize the CMake build type here (Release, Debug)
  BUILD_TYPE: Release
  RELEASE_VERSION: ${{github.ref_name}}

jobs:
  initialize:
    name: Prepare Release
    runs-on: ubuntu-latest
    steps:
      - name: Version Number
        run: echo create release for tag ${{github.ref_name}}

  build-windows:
    name: Build Windows Release-x86_64
    needs: initialize
    runs-on: windows-latest
    steps:
      - name: Install Qt
        uses: jurplel/install-qt-action@v3

      - name: Checkout Source
        uses: actions/checkout@v2
        with:
          submodules: "true"

      - name: Cache CEF folders
        uses: actions/cache@v3
        with:
          path: ${{github.workspace}}/CefViewCore/dep
          key: ${{ runner.os }}-${{ hashFiles('**/lockfiles') }}

      - name: Configure CMake
        run: cmake -B ${{github.workspace}}/build -A x64 -DPROJECT_ARCH=x86_64 -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} -DBUILD_DEMO=ON -DCMAKE_INSTALL_PREFIX:PATH=${{github.workspace}}/out/install

      - name: Build
        run: cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Install
        run: cmake --install ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Create SDK artifact
        uses: actions/upload-artifact@v4
        with:
          name: QCefView.windows.x86_64
          path: ${{github.workspace}}/out/install/QCefView

      - name: Create Demo artifact
        uses: actions/upload-artifact@v4
        with:
          name: QCefViewTest.windows.x86_64
          path: ${{github.workspace}}/out/install/QCefViewTest

  build-macos:
    name: Build macOS Release-x86_64
    needs: initialize
    runs-on: macos-latest
    steps:
      - name: Install Qt
        uses: jurplel/install-qt-action@v3
        with:
          setup-python: "true"

      - name: Checkout Source
        uses: actions/checkout@v2
        with:
          submodules: "true"

      - name: Cache CEF folders
        uses: actions/cache@v3
        with:
          path: ${{github.workspace}}/CefViewCore/dep
          key: ${{ runner.os }}-${{ hashFiles('**/lockfiles') }}

      - name: Configure CMake
        run: cmake -G "Xcode" -B ${{github.workspace}}/build -DPROJECT_ARCH=x86_64 -DBUILD_DEMO=ON -DUSE_SANDBOX=ON -DCMAKE_INSTALL_PREFIX:PATH=${{github.workspace}}/out/install

      - name: Build QCefView
        run: xcodebuild -project ${{github.workspace}}/build/QCefView.xcodeproj -target QCefView -configuration ${{env.BUILD_TYPE}}

      - name: Build QCefViewTest
        run: xcodebuild -project ${{github.workspace}}/build/QCefView.xcodeproj -target QCefViewTest -configuration ${{env.BUILD_TYPE}}

      - name: Install
        # Collect the output
        run: cmake --install ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Create SDK artifact
        uses: actions/upload-artifact@v4
        # Upload artifact
        with:
          name: QCefView.macos.x86_64
          path: ${{github.workspace}}/out/install/QCefView

      - name: Create Demo artifact
        uses: actions/upload-artifact@v4
        # Upload artifact
        with:
          name: QCefViewTest.macos.x86_64
          path: ${{github.workspace}}/out/install/QCefViewTest

  build-linux:
    name: Build Linux Release-x86_64
    needs: initialize
    runs-on: ubuntu-latest
    steps:
      - name: Install Qt
        uses: jurplel/install-qt-action@v3

      - name: Checkout Source
        uses: actions/checkout@v2
        with:
          submodules: "true"

      - name: Cache CEF folders
        uses: actions/cache@v3
        with:
          path: ${{github.workspace}}/CefViewCore/dep
          key: ${{ runner.os }}-${{ hashFiles('**/lockfiles') }}

      - name: Configure CMake
        run: cmake -B ${{github.workspace}}/build -DPROJECT_ARCH=x86_64 -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} -DBUILD_DEMO=ON -DUSE_SANDBOX=ON -DCMAKE_INSTALL_PREFIX:PATH=${{github.workspace}}/out/install

      - name: Build
        # Build your program with the given configuration
        run: cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Install
        # Collect the output
        run: cmake --install ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Create SDK artifact
        uses: actions/upload-artifact@v4
        # Upload artifact
        with:
          name: QCefView.linux.x86_64
          path: ${{github.workspace}}/out/install/QCefView

      - name: Create Demo artifact
        uses: actions/upload-artifact@v4
        # Upload artifact
        with:
          name: QCefViewTest.linux.x86_64
          path: ${{github.workspace}}/out/install/QCefViewTest

  archive-release:
    name: Create and Publish Release
    needs: [build-windows, build-macos, build-linux]
    runs-on: ubuntu-latest
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: ${{github.workspace}}/artifacts

      - name: Archive all artifacts
        run: for dir in ${{github.workspace}}/artifacts/*; do ( cd $dir && zip -r $dir-${{github.ref_name}}.zip . ) done

      - name: Publish Release
        uses: softprops/action-gh-release@v1
        with:
          name: QCefView-${{github.ref_name}}
          generate_release_notes: true
          files: ${{github.workspace}}/artifacts/*.zip
